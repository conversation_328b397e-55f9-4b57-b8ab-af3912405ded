{"name": "toc-dialog", "type": "registry:block", "dependencies": ["react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/dialog.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport { useRef, useState } from \"react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from \"@/components/ui/dialog\";\r\n\r\nexport default function TocDialog() {\r\n  const [hasReadToBottom, setHasReadToBottom] = useState(false);\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n\r\n  const handleScroll = () => {\r\n    const content = contentRef.current;\r\n    if (!content) return;\r\n\r\n    const scrollPercentage =\r\n      content.scrollTop / (content.scrollHeight - content.clientHeight);\r\n    if (scrollPercentage >= 0.99 && !hasReadToBottom) {\r\n      setHasReadToBottom(true);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog>\r\n      <DialogTrigger asChild>\r\n        <Button variant=\"outline\">Terms & Conditions</Button>\r\n      </DialogTrigger>\r\n      <DialogContent className=\"flex flex-col gap-0 p-0 sm:max-h-[min(640px,80vh)] sm:max-w-lg [&>button:last-child]:top-3.5\">\r\n        <DialogHeader className=\"contents space-y-0 text-left\">\r\n          <DialogTitle className=\"border-b px-6 py-4 text-base\">\r\n            Terms & Conditions\r\n          </DialogTitle>\r\n          <div\r\n            ref={contentRef}\r\n            onScroll={handleScroll}\r\n            className=\"overflow-y-auto\"\r\n          >\r\n            <DialogDescription asChild>\r\n              <div className=\"px-6 py-4\">\r\n                <div className=\"space-y-4 [&_strong]:font-semibold [&_strong]:text-foreground\">\r\n                  <div className=\"space-y-4\">\r\n                    <div className=\"space-y-1\">\r\n                      <p>\r\n                        <strong>Acceptance of Terms</strong>\r\n                      </p>\r\n                      <p>\r\n                        By accessing and using this website, users agree to\r\n                        comply with and be bound by these Terms of Service.\r\n                        Users who do not agree with these terms should\r\n                        discontinue use of the website immediately.\r\n                      </p>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-1\">\r\n                      <p>\r\n                        <strong>User Account Responsibilities</strong>\r\n                      </p>\r\n                      <p>\r\n                        Users are responsible for maintaining the\r\n                        confidentiality of their account credentials. Any\r\n                        activities occurring under a user&lsquo;s account are\r\n                        the sole responsibility of the account holder. Users\r\n                        must notify the website administrators immediately of\r\n                        any unauthorized account access.\r\n                      </p>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-1\">\r\n                      <p>\r\n                        <strong>Content Usage and Restrictions</strong>\r\n                      </p>\r\n                      <p>\r\n                        The website and its original content are protected by\r\n                        intellectual property laws. Users may not reproduce,\r\n                        distribute, modify, create derivative works, or\r\n                        commercially exploit any content without explicit\r\n                        written permission from the website owners.\r\n                      </p>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-1\">\r\n                      <p>\r\n                        <strong>Limitation of Liability</strong>\r\n                      </p>\r\n                      <p>\r\n                        The website provides content &ldquo;as is&ldquo; without\r\n                        any warranties. The website owners shall not be liable\r\n                        for direct, indirect, incidental, consequential, or\r\n                        punitive damages arising from user interactions with the\r\n                        platform.\r\n                      </p>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-1\">\r\n                      <p>\r\n                        <strong>User Conduct Guidelines</strong>\r\n                      </p>\r\n                      <ul className=\"list-disc pl-6\">\r\n                        <li>Not upload harmful or malicious content</li>\r\n                        <li>Respect the rights of other users</li>\r\n                        <li>\r\n                          Avoid activities that could disrupt website\r\n                          functionality\r\n                        </li>\r\n                        <li>\r\n                          Comply with applicable local and international laws\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-1\">\r\n                      <p>\r\n                        <strong>Modifications to Terms</strong>\r\n                      </p>\r\n                      <p>\r\n                        The website reserves the right to modify these terms at\r\n                        any time. Continued use of the website after changes\r\n                        constitutes acceptance of the new terms.\r\n                      </p>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-1\">\r\n                      <p>\r\n                        <strong>Termination Clause</strong>\r\n                      </p>\r\n                      <p>\r\n                        The website may terminate or suspend user access without\r\n                        prior notice for violations of these terms or for any\r\n                        other reason deemed appropriate by the administration.\r\n                      </p>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-1\">\r\n                      <p>\r\n                        <strong>Governing Law</strong>\r\n                      </p>\r\n                      <p>\r\n                        These terms are governed by the laws of the jurisdiction\r\n                        where the website is primarily operated, without regard\r\n                        to conflict of law principles.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </DialogDescription>\r\n          </div>\r\n        </DialogHeader>\r\n        <DialogFooter className=\"border-t px-6 py-4 sm:items-center\">\r\n          {!hasReadToBottom && (\r\n            <span className=\"grow text-xs text-muted-foreground max-sm:text-center\">\r\n              Read all terms before accepting.\r\n            </span>\r\n          )}\r\n          <DialogClose asChild>\r\n            <Button type=\"button\" variant=\"outline\">\r\n              Cancel\r\n            </Button>\r\n          </DialogClose>\r\n          <DialogClose asChild>\r\n            <Button type=\"button\" disabled={!hasReadToBottom}>\r\n              I agree\r\n            </Button>\r\n          </DialogClose>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/basics/modals/toc-dialog.tsx", "target": "/components/mvpblocks/toc-dialog.tsx"}]}