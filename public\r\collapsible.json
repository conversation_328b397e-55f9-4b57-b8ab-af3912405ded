{"name": "collapsible", "type": "registry:ui", "dependencies": ["@radix-ui/react-collapsible"], "files": [{"type": "registry:ui", "content": "\"use client\";\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\";\r\n\r\nconst Collapsible = CollapsiblePrimitive.Root;\r\n\r\nconst CollapsibleTrigger = CollapsiblePrimitive.CollapsibleTrigger;\r\n\r\nconst CollapsibleContent = CollapsiblePrimitive.CollapsibleContent;\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent };\r\n", "path": "/components/ui/collapsible.tsx", "target": "/components/ui/collapsible.tsx"}]}