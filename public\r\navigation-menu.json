{"name": "navigation-menu", "type": "registry:ui", "dependencies": ["@radix-ui/react-navigation-menu"], "files": [{"type": "registry:ui", "content": "import * as React from \"react\";\r\nimport * as NavigationMenuPrimitive from \"@radix-ui/react-navigation-menu\";\r\nimport { cva } from \"class-variance-authority\";\r\nimport { ChevronDown } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst NavigationMenu = React.forwardRef<\r\n  React.ElementRef<typeof NavigationMenuPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Root>\r\n>(({ className, children, ...props }, ref) => (\r\n  <NavigationMenuPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative z-10 flex max-w-max flex-1 items-center justify-center\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <NavigationMenuViewport />\r\n  </NavigationMenuPrimitive.Root>\r\n));\r\nNavigationMenu.displayName = NavigationMenuPrimitive.Root.displayName;\r\n\r\nconst NavigationMenuList = React.forwardRef<\r\n  React.ElementRef<typeof NavigationMenuPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <NavigationMenuPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"group flex flex-1 list-none items-center justify-center space-x-1\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nNavigationMenuList.displayName = NavigationMenuPrimitive.List.displayName;\r\n\r\nconst NavigationMenuItem = NavigationMenuPrimitive.Item;\r\n\r\nconst navigationMenuTriggerStyle = cva(\r\n  \"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[state=open]:text-accent-foreground data-[state=open]:bg-accent/50 data-[state=open]:hover:bg-accent data-[state=open]:focus:bg-accent\",\r\n);\r\n\r\nconst NavigationMenuTrigger = React.forwardRef<\r\n  React.ElementRef<typeof NavigationMenuPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <NavigationMenuPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(navigationMenuTriggerStyle(), \"group\", className)}\r\n    {...props}\r\n  >\r\n    {children}{\" \"}\r\n    <ChevronDown\r\n      className=\"relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180\"\r\n      aria-hidden=\"true\"\r\n    />\r\n  </NavigationMenuPrimitive.Trigger>\r\n));\r\nNavigationMenuTrigger.displayName = NavigationMenuPrimitive.Trigger.displayName;\r\n\r\nconst NavigationMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof NavigationMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <NavigationMenuPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nNavigationMenuContent.displayName = NavigationMenuPrimitive.Content.displayName;\r\n\r\nconst NavigationMenuLink = NavigationMenuPrimitive.Link;\r\n\r\nconst NavigationMenuViewport = React.forwardRef<\r\n  React.ElementRef<typeof NavigationMenuPrimitive.Viewport>,\r\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Viewport>\r\n>(({ className, ...props }, ref) => (\r\n  <div className={cn(\"absolute left-0 top-full flex justify-center\")}>\r\n    <NavigationMenuPrimitive.Viewport\r\n      className={cn(\r\n        \"origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)]\",\r\n        className,\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  </div>\r\n));\r\nNavigationMenuViewport.displayName =\r\n  NavigationMenuPrimitive.Viewport.displayName;\r\n\r\nconst NavigationMenuIndicator = React.forwardRef<\r\n  React.ElementRef<typeof NavigationMenuPrimitive.Indicator>,\r\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Indicator>\r\n>(({ className, ...props }, ref) => (\r\n  <NavigationMenuPrimitive.Indicator\r\n    ref={ref}\r\n    className={cn(\r\n      \"top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <div className=\"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md\" />\r\n  </NavigationMenuPrimitive.Indicator>\r\n));\r\nNavigationMenuIndicator.displayName =\r\n  NavigationMenuPrimitive.Indicator.displayName;\r\n\r\nexport {\r\n  navigationMenuTriggerStyle,\r\n  NavigationMenu,\r\n  NavigationMenuList,\r\n  NavigationMenuItem,\r\n  NavigationMenuContent,\r\n  NavigationMenuTrigger,\r\n  NavigationMenuLink,\r\n  NavigationMenuIndicator,\r\n  NavigationMenuViewport,\r\n};\r\n", "path": "/components/ui/navigation-menu.tsx", "target": "/components/ui/navigation-menu.tsx"}]}