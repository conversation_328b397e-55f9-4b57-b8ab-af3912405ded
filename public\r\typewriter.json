{"name": "typewriter", "type": "registry:ui", "dependencies": ["framer-motion", "react"], "registryDependencies": [], "files": [{"type": "registry:ui", "content": "\"use client\";\r\nimport { useEffect } from \"react\";\r\nimport { motion, useMotionValue, useTransform, animate } from \"framer-motion\";\r\n\r\nexport default function TextGenerateEffect({\r\n  words,\r\n  className = \"\",\r\n}: {\r\n  words: string;\r\n  className?: string;\r\n}) {\r\n  const count = useMotionValue(0);\r\n  const rounded = useTransform(count, (latest) => Math.round(latest));\r\n  const displayText = useTransform(rounded, (latest) =>\r\n    words.slice(0, latest)\r\n  );\r\n\r\n  useEffect(() => {\r\n    const controls = animate(count, words.length, {\r\n      type: \"tween\",\r\n      duration: 2.5, // Increased from 1 to 2.5 seconds\r\n      ease: \"easeInOut\",\r\n    });\r\n    return controls.stop;\r\n  }, [words]);\r\n\r\n  return (\r\n    <motion.span className={className}>\r\n      {displayText}\r\n    </motion.span>\r\n  );\r\n};", "path": "/components/ui/typewriter.tsx", "target": "/components/ui/typewriter.tsx"}]}