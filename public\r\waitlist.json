{"name": "waitlist", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "next-themes", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/particles.json", "https://blocks.mvp-subha.me/r/spotlight.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport type React from \"react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { ArrowR<PERSON>, <PERSON>rk<PERSON>, Code, Star, ExternalLink } from \"lucide-react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport Image from \"next/image\";\r\nimport { Particles } from \"@/components/ui/particles\";\r\nimport { Spotlight } from \"@/components/ui/spotlight\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { Bricolage_Grotesque } from \"next/font/google\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst brico = Bricolage_Grotesque({\r\n  subsets: [\"latin\"],\r\n});\r\n\r\n// Sample users for the waitlist display\r\nconst users = [\r\n  { imgUrl: \"https://avatars.githubusercontent.com/u/111780029\" },\r\n  { imgUrl: \"https://avatars.githubusercontent.com/u/123104247\" },\r\n  { imgUrl: \"https://avatars.githubusercontent.com/u/115650165\" },\r\n  { imgUrl: \"https://avatars.githubusercontent.com/u/71373838\" },\r\n];\r\n\r\nexport default function WaitlistPage() {\r\n  const [email, setEmail] = useState(\"\");\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [submitted, setSubmitted] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const { resolvedTheme } = useTheme();\r\n  const [color, setColor] = useState(\"#ffffff\");\r\n\r\n  useEffect(() => {\r\n    setColor(resolvedTheme === \"dark\" ? \"#ffffff\" : \"#e60a64\");\r\n  }, [resolvedTheme]);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setIsSubmitting(true);\r\n    setError(null);\r\n\r\n    // Your form submission logic here\r\n    // For now, let's just simulate a delay\r\n    await new Promise((resolve) => setTimeout(resolve, 1000));\r\n    setSubmitted(true);\r\n    setIsSubmitting(false);\r\n  };\r\n\r\n  return (\r\n    <main className=\"relative w-full flex min-h-screen items-center justify-center overflow-hidden xl:h-screen\">\r\n      <Spotlight />\r\n\r\n      <Particles\r\n        className=\"absolute inset-0 z-0\"\r\n        quantity={100}\r\n        ease={80}\r\n        refresh\r\n        color={color}\r\n      />\r\n\r\n      <div className=\"relative z-[100] mx-auto max-w-2xl px-4 py-16 text-center\">\r\n        {/* Badge */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.5 }}\r\n          className=\"mb-8 inline-flex items-center gap-2 rounded-full border border-primary/10 bg-gradient-to-r from-primary/15 to-primary/5 px-4 py-2 backdrop-blur-sm\"\r\n        >\r\n          <img\r\n            src=\"https://i.postimg.cc/vHnf0qZF/logo.webp\"\r\n            alt=\"logo\"\r\n            className=\"spin h-6 w-6\"\r\n          />\r\n          <span className=\"text-sm font-medium\">Mvpblocks</span>\r\n          <motion.div\r\n            animate={{ x: [0, 5, 0] }}\r\n            transition={{ duration: 1.5, repeat: Number.POSITIVE_INFINITY }}\r\n          >\r\n            <ArrowRight className=\"h-4 w-4\" />\r\n          </motion.div>\r\n        </motion.div>\r\n\r\n        <motion.h1\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 1, delay: 0.2 }}\r\n          className={cn(\r\n            \"mb-4 cursor-crosshair bg-gradient-to-b from-foreground via-foreground/80 to-foreground/40 bg-clip-text text-4xl font-bold text-transparent sm:text-7xl\",\r\n            brico.className,\r\n          )}\r\n        >\r\n          Join the{\" \"}\r\n          <span className=\"bg-primary from-foreground via-rose-300 to-primary bg-clip-text text-transparent dark:bg-gradient-to-b\">\r\n            Waitlist\r\n          </span>\r\n        </motion.h1>\r\n\r\n        {/* Subtitle */}\r\n        <motion.p\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 1, delay: 0.5 }}\r\n          className=\"mb-12 mt-2 text-muted-foreground sm:text-lg\"\r\n        >\r\n          Be the first to access our revolutionary component library.\r\n          <br className=\"hidden sm:block\" /> Build your MVP faster than ever\r\n          before.\r\n        </motion.p>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8, delay: 0.7 }}\r\n          className=\"mb-12 grid grid-cols-2 gap-6 sm:grid-cols-3\"\r\n        >\r\n          <div\r\n            className={cn(\r\n              \"flex flex-col items-center justify-center rounded-xl border border-primary/10 bg-white/5 p-4 backdrop-blur-md\",\r\n              resolvedTheme === \"dark\" ? \"glass\" : \"glass2\",\r\n            )}\r\n          >\r\n            <Code className=\"mb-2 h-5 w-5 text-primary\" />\r\n            <span className=\"text-xl font-bold\">100+</span>\r\n            <span className=\"text-xs text-muted-foreground\">Components</span>\r\n          </div>\r\n\r\n          <div\r\n            className={cn(\r\n              \"flex flex-col items-center justify-center rounded-xl border border-primary/10 bg-white/5 p-4 backdrop-blur-md\",\r\n              resolvedTheme === \"dark\" ? \"glass\" : \"glass2\",\r\n            )}\r\n          >\r\n            <ExternalLink className=\"mb-2 h-5 w-5 text-primary\" />\r\n            <span className=\"text-xl font-bold\">Open Source</span>\r\n            <span className=\"text-xs text-muted-foreground\">BSD 3-Clause</span>\r\n          </div>\r\n\r\n          <div\r\n            className={cn(\r\n              \"flex flex-col items-center justify-center rounded-xl border border-primary/10 bg-white/5 p-4 backdrop-blur-md\",\r\n              resolvedTheme === \"dark\" ? \"glass\" : \"glass2\",\r\n            )}\r\n          >\r\n            <Star className=\"mb-2 h-5 w-5 text-primary\" />\r\n            <span className=\"text-xl font-bold\">Premium</span>\r\n            <span className=\"text-xs text-muted-foreground\">Quality</span>\r\n          </div>\r\n\r\n          <div\r\n            className={cn(\r\n              \"flex flex-col items-center justify-center rounded-xl border border-primary/10 bg-white/5 p-4 backdrop-blur-md sm:hidden\",\r\n              resolvedTheme === \"dark\" ? \"glass\" : \"glass2\",\r\n            )}\r\n          >\r\n            <Code className=\"mb-2 h-5 w-5 text-primary\" />\r\n            <span className=\"text-xl font-bold\">15+</span>\r\n            <span className=\"text-xs text-muted-foreground\">Categories</span>\r\n          </div>\r\n        </motion.div>\r\n\r\n        <motion.form\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.5, delay: 0.4 }}\r\n          onSubmit={handleSubmit}\r\n          className=\"mx-auto flex flex-col gap-4 sm:flex-row\"\r\n        >\r\n          <AnimatePresence mode=\"wait\">\r\n            {!submitted ? (\r\n              <>\r\n                <div className=\"relative flex-1\">\r\n                  <motion.input\r\n                    key=\"email-input\"\r\n                    initial={{ opacity: 0, y: 10 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    exit={{ opacity: 0, y: -10 }}\r\n                    transition={{ duration: 0.3 }}\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    id=\"email\"\r\n                    placeholder=\"Enter your email\"\r\n                    value={email}\r\n                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>\r\n                      setEmail(e.target.value)\r\n                    }\r\n                    required\r\n                    className=\"w-full rounded-xl border border-primary/20 bg-white/5 px-6 py-4 text-foreground backdrop-blur-md transition-all placeholder:text-muted-foreground/70 focus:border-primary/50 focus:outline-none focus:ring-2 focus:ring-primary/30\"\r\n                  />\r\n                  {error && (\r\n                    <motion.p\r\n                      initial={{ opacity: 0 }}\r\n                      animate={{ opacity: 1 }}\r\n                      className=\"mt-2 rounded-xl border border-destructive/40 bg-destructive/10 px-4 py-1 text-sm text-destructive sm:absolute\"\r\n                    >\r\n                      {error}\r\n                    </motion.p>\r\n                  )}\r\n                </div>\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={isSubmitting || submitted}\r\n                  className=\"group relative overflow-hidden rounded-xl bg-gradient-to-b from-rose-500 to-rose-700 px-8 py-4 font-semibold text-primary-foreground text-white shadow-[0px_2px_0px_0px_rgba(255,255,255,0.3)_inset] transition-all duration-300 hover:shadow-[0_0_20px_rgba(236,72,153,0.4)] focus:outline-none focus:ring-2 focus:ring-primary/50 active:scale-95 disabled:cursor-not-allowed disabled:opacity-50\"\r\n                >\r\n                  <span className=\"relative z-10 flex items-center justify-center gap-2\">\r\n                    {isSubmitting ? \"Joining...\" : \"Join Waitlist\"}\r\n                    <Sparkles className=\"h-4 w-4 transition-all duration-300 group-hover:rotate-12\" />\r\n                  </span>\r\n                  <span className=\"absolute inset-0 z-0 bg-gradient-to-r from-rose-500 to-primary opacity-0 transition-opacity duration-300 group-hover:opacity-100\"></span>\r\n                </button>\r\n              </>\r\n            ) : (\r\n              <motion.div\r\n                key=\"thank-you-message\"\r\n                initial={{ opacity: 0, y: 10 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                exit={{ opacity: 0, y: -10 }}\r\n                transition={{ duration: 0.6 }}\r\n                className={cn(\r\n                  \"flex-1 cursor-pointer rounded-xl border border-primary/20 bg-gradient-to-r from-primary/10 via-transparent to-primary/10 px-6 py-4 font-medium text-primary backdrop-blur-md transition-all duration-300 hover:shadow-[0_0_20px_rgba(236,72,153,0.3)] active:brightness-125\",\r\n                  resolvedTheme === \"dark\" ? \"glass\" : \"glass2\",\r\n                )}\r\n              >\r\n                <span className=\"flex items-center justify-center gap-2\">\r\n                  Thanks for joining!{\" \"}\r\n                  <Sparkles className=\"h-4 w-4 animate-pulse\" />\r\n                </span>\r\n              </motion.div>\r\n            )}\r\n          </AnimatePresence>\r\n        </motion.form>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ duration: 0.5, delay: 1 }}\r\n          className=\"mt-10 flex items-center justify-center gap-1\"\r\n        >\r\n          <div className=\"flex -space-x-3\">\r\n            {users.map((user, i) => (\r\n              <motion.div\r\n                key={i}\r\n                initial={{ scale: 0, x: -10 }}\r\n                animate={{ scale: 1, x: 0 }}\r\n                transition={{ duration: 0.4, delay: 1 + i * 0.2 }}\r\n                className=\"size-10 rounded-full border-2 border-background bg-gradient-to-r from-primary to-rose-500 p-[2px]\"\r\n              >\r\n                <div className=\"overflow-hidden rounded-full\">\r\n                  <Image\r\n                    src={user.imgUrl}\r\n                    alt=\"Avatar\"\r\n                    className=\"rounded-full transition-all duration-300 hover:rotate-6 hover:scale-110\"\r\n                    width={40}\r\n                    height={40}\r\n                  />\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n          <motion.span\r\n            initial={{ opacity: 0, x: -10 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.5, delay: 1.3 }}\r\n            className=\"ml-2 text-muted-foreground\"\r\n          >\r\n            <span className=\"font-semibold text-primary\">100+</span> already\r\n            joined ✨\r\n          </motion.span>\r\n        </motion.div>\r\n      </div>\r\n\r\n      <style jsx global>{`\r\n        @keyframes float {\r\n          0%,\r\n          100% {\r\n            transform: translateY(0) translateX(0);\r\n            opacity: 0.3;\r\n          }\r\n          25% {\r\n            transform: translateY(-20px) translateX(10px);\r\n            opacity: 0.8;\r\n          }\r\n          50% {\r\n            transform: translateY(-40px) translateX(-10px);\r\n            opacity: 0.4;\r\n          }\r\n          75% {\r\n            transform: translateY(-20px) translateX(10px);\r\n            opacity: 0.6;\r\n          }\r\n        }\r\n      `}</style>\r\n    </main>\r\n  );\r\n}", "path": "/components/mvpblocks/pages/waitlist.tsx", "target": "/components/mvpblocks/waitlist.tsx"}]}