{"name": "team-2", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "\"use client\";\n\nimport React from \"react\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport { cn } from \"@/lib/utils\";\nimport { motion } from \"framer-motion\";\nimport { GithubIcon, LinkedinIcon, TwitterIcon } from \"lucide-react\";\n\ninterface TeamMember {\n  name: string;\n  role: string;\n  imageUrl: string;\n  socialLinks?: { platform: \"github\" | \"twitter\" | \"linkedin\"; url: string }[];\n}\n\ninterface TeamProps {\n  title?: string;\n  subtitle?: string;\n  members?: TeamMember[];\n  className?: string;\n}\n\n// Default team members data\nconst defaultMembers: TeamMember[] = [\n  {\n    name: \"<PERSON>\",\n    role: \"CEO & Co-Founder\",\n    imageUrl: \"https://images.unsplash.com/photo-1568602471122-7832951cc4c5?q=80&w=400&h=400&auto=format&fit=crop\",\n    socialLinks: [\n      { platform: \"twitter\", url: \"https://twitter.com\" },\n      { platform: \"github\", url: \"https://github.com\" },\n      { platform: \"linkedin\", url: \"https://linkedin.com\" },\n    ],\n  },\n  {\n    name: \"<PERSON>\",\n    role: \"CTO & Co-Founder\",\n    imageUrl: \"https://images.unsplash.com/photo-1580489944761-15a19d654956?q=80&w=400&h=400&auto=format&fit=crop\",\n    socialLinks: [\n      { platform: \"twitter\", url: \"https://twitter.com\" },\n      { platform: \"github\", url: \"https://github.com\" },\n      { platform: \"linkedin\", url: \"https://linkedin.com\" },\n    ],\n  },\n  {\n    name: \"Joseph McFall\",\n    role: \"Front-end Developer\",\n    imageUrl: \"https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?q=80&w=400&h=400&auto=format&fit=crop\",\n    socialLinks: [\n      { platform: \"github\", url: \"https://github.com\" },\n      { platform: \"linkedin\", url: \"https://linkedin.com\" },\n    ],\n  },\n  {\n    name: \"Helene Engels\",\n    role: \"Front-end Developer\",\n    imageUrl: \"https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=400&h=400&auto=format&fit=crop\",\n    socialLinks: [\n      { platform: \"twitter\", url: \"https://twitter.com\" },\n      { platform: \"github\", url: \"https://github.com\" },\n    ],\n  },\n  {\n    name: \"Thom Belly\",\n    role: \"UI/UX Designer\",\n    imageUrl: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=400&h=400&auto=format&fit=crop\",\n    socialLinks: [\n      { platform: \"twitter\", url: \"https://twitter.com\" },\n      { platform: \"linkedin\", url: \"https://linkedin.com\" },\n    ],\n  },\n  {\n    name: \"Bonnie Green\",\n    role: \"Product Manager\",\n    imageUrl: \"https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=400&h=400&auto=format&fit=crop\",\n    socialLinks: [\n      { platform: \"github\", url: \"https://github.com\" },\n      { platform: \"linkedin\", url: \"https://linkedin.com\" },\n    ],\n  },\n  {\n    name: \"Roberta Casas\",\n    role: \"Content Strategist\",\n    imageUrl: \"https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?q=80&w=400&h=400&auto=format&fit=crop\",\n    socialLinks: [\n      { platform: \"twitter\", url: \"https://twitter.com\" },\n      { platform: \"linkedin\", url: \"https://linkedin.com\" },\n    ],\n  },\n  {\n    name: \"Jesse Leos\",\n    role: \"Back-end Developer\",\n    imageUrl: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=400&h=400&auto=format&fit=crop\",\n    socialLinks: [\n      { platform: \"github\", url: \"https://github.com\" },\n      { platform: \"linkedin\", url: \"https://linkedin.com\" },\n    ],\n  },\n];\n\nexport default function Team2({\n  title = \"Our people make us great\",\n  subtitle = \"You'll interact with talented professionals, will be challenged to solve difficult problems and think in new and creative ways.\",\n  members = defaultMembers,\n  className,\n}: TeamProps) {\n  return (\n    <section className={cn(\"relative py-16 md:py-24 overflow-hidden w-full\", className)}>\n      {/* Background decorative elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute inset-0 bg-[radial-gradient(ellipse_at_center,hsl(var(--primary)/0.15),transparent_70%)]\" />\n        <div className=\"absolute top-1/4 left-1/4 h-64 w-64 rounded-full bg-primary/5 blur-3xl\" />\n        <div className=\"absolute bottom-1/4 right-1/4 h-64 w-64 rounded-full bg-primary/10 blur-3xl\" />\n      </div>\n\n      <div className=\"container mx-auto px-4 md:px-6\">\n        {/* Section header */}\n        <motion.div \n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          viewport={{ once: true }}\n          className=\"mx-auto mb-12 md:mb-16 text-center max-w-3xl\"\n        >\n          <h2 className=\"text-3xl font-bold tracking-tight md:text-4xl lg:text-5xl mb-4 bg-gradient-to-r from-foreground/80 via-foreground to-foreground/80 bg-clip-text text-transparent dark:from-foreground/70 dark:via-foreground dark:to-foreground/70\">\n            {title}\n          </h2>\n          <p className=\"text-muted-foreground md:text-lg\">\n            {subtitle}\n          </p>\n        </motion.div>\n\n        {/* Team members grid */}\n        <div className=\"grid max-w-7xl mx-auto grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 md:gap-8\">\n          {members.map((member, index) => (\n            <TeamMemberCard \n              key={member.name} \n              member={member} \n              index={index}\n            />\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n\nfunction TeamMemberCard({ member, index }: { member: TeamMember; index: number }) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.4, delay: 0.1 * (index % 4) }}\n      viewport={{ once: true }}\n      className=\"group relative overflow-hidden rounded-xl\"\n    >\n      {/* Image container */}\n      <div className=\"relative aspect-square overflow-hidden rounded-xl bg-muted\">\n        <div className=\"absolute inset-0 bg-gradient-to-t from-background/80 via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100 z-10\" />\n        \n        <Image\n          src={member.imageUrl}\n          alt={member.name}\n          fill\n          sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw\"\n          className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n        />\n        \n        {/* Social links that appear on hover */}\n        {member.socialLinks && (\n          <div className=\"absolute bottom-4 left-0 right-0 z-20 flex justify-center gap-3 opacity-0 transition-opacity duration-300 group-hover:opacity-100\">\n            {member.socialLinks.map((link) => (\n              <Link\n                key={link.platform}\n                href={link.url}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"flex h-9 w-9 items-center justify-center rounded-full bg-background/80 text-foreground backdrop-blur-sm transition-all hover:bg-primary hover:text-primary-foreground\"\n              >\n                {link.platform === \"github\" && <GithubIcon className=\"h-5 w-5\" />}\n                {link.platform === \"twitter\" && <TwitterIcon className=\"h-5 w-5\" />}\n                {link.platform === \"linkedin\" && <LinkedinIcon className=\"h-5 w-5\" />}\n              </Link>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Name and role */}\n      <div className=\"mt-4 text-center\">\n        <h3 className=\"text-lg font-semibold\">{member.name}</h3>\n        <p className=\"text-sm text-primary\">{member.role}</p>\n      </div>\n    </motion.div>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/team/team-2.tsx", "target": "/components/mvpblocks/team-2.tsx"}]}