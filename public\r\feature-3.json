{"name": "feature-3", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "\r\nimport {\r\n  Building2,\r\n  Lightbulb,\r\n  ScreenShare,\r\n  Trophy,\r\n  User,\r\n  User2,\r\n  LucideIcon,\r\n} from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\n// Define the feature item type\r\ntype FeatureItem = {\r\n  icon: LucideIcon;\r\n  title: string;\r\n  description: string;\r\n  position?: \"left\" | \"right\";\r\n  cornerStyle?: string;\r\n};\r\n\r\n// Create feature data arrays for left and right columns\r\nconst leftFeatures: FeatureItem[] = [\r\n  {\r\n    icon: Building2,\r\n    title: \"Taught by Professionals\",\r\n    description: \"Learn directly from top engineers and founders with real-world experience.\",\r\n    position: \"left\",\r\n    cornerStyle: \"sm:translate-x-4 sm:rounded-br-[2px]\",\r\n  },\r\n  {\r\n    icon: User2,\r\n    title: \"Coding Hostels\",\r\n    description: \"Join virtual hostels to study, collaborate, and vibe with fellow learners.\",\r\n    position: \"left\",\r\n    cornerStyle: \"sm:-translate-x-4 sm:rounded-br-[2px]\",\r\n  },\r\n  {\r\n    icon: Trophy,\r\n    title: \"Bounties\",\r\n    description: \"Win rewards for solving challenges, contributing to projects, and helping peers.\",\r\n    position: \"left\",\r\n    cornerStyle: \"sm:translate-x-4 sm:rounded-tr-[2px]\",\r\n  },\r\n];\r\n\r\nconst rightFeatures: FeatureItem[] = [\r\n  {\r\n    icon: ScreenShare,\r\n    title: \"Revision Classes\",\r\n    description: \"Stay sharp with weekly revision sessions and topic refreshers.\",\r\n    position: \"right\",\r\n    cornerStyle: \"sm:-translate-x-4 sm:rounded-bl-[2px]\",\r\n  },\r\n  {\r\n    icon: User,\r\n    title: \"Peer Code Reviews\",\r\n    description: \"Improve faster with feedback from mentors and batchmates on your actual code.\",\r\n    position: \"right\",\r\n    cornerStyle: \"sm:translate-x-4 sm:rounded-bl-[2px]\",\r\n  },\r\n  {\r\n    icon: Lightbulb,\r\n    title: \"Leet Lab\",\r\n    description: \"Ace coding interviews with daily DSA problems, contests, and tracking.\",\r\n    position: \"right\",\r\n    cornerStyle: \"sm:-translate-x-4 sm:rounded-tl-[2px]\",\r\n  },\r\n];\r\n\r\n// Feature card component\r\nconst FeatureCard = ({ feature }: { feature: FeatureItem }) => {\r\n  const Icon = feature.icon;\r\n\r\n  return (\r\n    <div>\r\n      <div className={cn(\r\n        \"relative rounded-2xl px-4 pb-4 pt-4 text-sm\",\r\n        \"bg-secondary/50 ring ring-border\",\r\n        feature.cornerStyle\r\n      )}>\r\n        <div className=\"mb-3 text-[2rem] text-primary\">\r\n          <Icon />\r\n        </div>\r\n        <h2 className=\"mb-2.5 text-2xl text-foreground\">\r\n          {feature.title}\r\n        </h2>\r\n        <p className=\"text-pretty text-base text-muted-foreground\">\r\n          {feature.description}\r\n        </p>\r\n        {/* Decorative elements */}\r\n        <span className=\"absolute -bottom-px left-1/2 h-px w-1/2 -translate-x-1/2 bg-gradient-to-r from-primary/0 via-primary to-primary/0 opacity-60\"></span>\r\n        <span className=\"absolute inset-0 bg-[radial-gradient(30%_5%_at_50%_100%,hsl(var(--primary)/0.15)_0%,transparent_100%)] opacity-60\"></span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default function Feature3() {\r\n\r\n  return (\r\n    <section className=\"pb-8 pt-20\" id=\"features\">\r\n      <div className=\"mx-6 max-w-[1120px] pb-16 pt-2 max-[300px]:mx-4 min-[1150px]:mx-auto\">\r\n        <div className=\"flex flex-col-reverse gap-6 md:grid md:grid-cols-3\">\r\n          {/* Left column */}\r\n          <div className=\"flex flex-col gap-6\">\r\n            {leftFeatures.map((feature, index) => (\r\n              <FeatureCard key={`left-feature-${index}`} feature={feature} />\r\n            ))}\r\n          </div>\r\n\r\n          {/* Center column */}\r\n          <div className=\"order-[1] mb-6 self-center sm:order-[0] md:mb-0\">\r\n            <div className=\"mb-4.5 relative mx-auto w-fit rounded-full rounded-bl-[2px] bg-secondary px-4 py-2 text-sm text-foreground ring ring-border\">\r\n              <span className=\"z-1 relative flex items-center gap-2\">\r\n                Features\r\n              </span>\r\n              <span className=\"absolute -bottom-px left-1/2 h-px w-2/5 -translate-x-1/2 bg-gradient-to-r from-primary/0 via-primary to-primary/0\"></span>\r\n              <span className=\"absolute inset-0 bg-[radial-gradient(30%_40%_at_50%_100%,hsl(var(--primary)/0.25)_0%,transparent_100%)]\"></span>\r\n            </div>\r\n            <h2 className=\"mb-2 text-center text-2xl text-foreground sm:mb-2.5 md:text-[2rem]\">\r\n              Key Benefits of Cohorts\r\n            </h2>\r\n            <p className=\"mx-auto max-w-[18rem] text-pretty text-center text-muted-foreground\">\r\n              Cohorts are best way to learn because you finish the course in a\r\n              timely manner\r\n            </p>\r\n          </div>\r\n\r\n          {/* Right column */}\r\n          <div className=\"flex flex-col gap-6\">\r\n            {rightFeatures.map((feature, index) => (\r\n              <FeatureCard key={`right-feature-${index}`} feature={feature} />\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/features/feature-3.tsx", "target": "/components/mvpblocks/feature-3.tsx"}]}