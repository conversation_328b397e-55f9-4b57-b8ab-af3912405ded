{"name": "dot-card", "type": "registry:block", "dependencies": [], "registryDependencies": [], "files": [{"type": "registry:block", "content": "export default function DotCard() {\r\n  return (\r\n    <div className=\"relative mx-auto w-full max-w-sm rounded-lg border border-dashed border-zinc-300 px-4 dark:border-zinc-800 sm:px-6 md:px-8\">\r\n      <div className=\"absolute left-0 top-4 -z-0 h-px w-full bg-zinc-400 dark:bg-zinc-700 sm:top-6 md:top-8\" />\r\n      <div className=\"absolute bottom-4 left-0 z-0 h-px w-full bg-zinc-400 dark:bg-zinc-700 sm:bottom-6 md:bottom-8\" />\r\n      <div className=\"relative w-full border-x border-zinc-400 dark:border-zinc-700\">\r\n        <div className=\"absolute z-0 grid h-full w-full items-center\">\r\n          <section className=\"absolute z-0 grid h-full w-full grid-cols-2 place-content-between\">\r\n            <div className=\"my-4 size-1 -translate-x-[2.5px] rounded-full bg-primary outline outline-8 outline-gray-50 dark:outline-gray-950 sm:my-6 md:my-8\" />\r\n            <div className=\"my-4 size-1 translate-x-[2.5px] place-self-end rounded-full bg-primary outline outline-8 outline-gray-50 dark:outline-gray-950 sm:my-6 md:my-8\" />\r\n            <div className=\"my-4 size-1 -translate-x-[2.5px] rounded-full bg-primary outline outline-8 outline-gray-50 dark:outline-gray-950 sm:my-6 md:my-8\" />\r\n            <div className=\"my-4 size-1 translate-x-[2.5px] place-self-end rounded-full bg-primary outline outline-8 outline-gray-50 dark:outline-gray-950 sm:my-6 md:my-8\" />\r\n          </section>\r\n        </div>\r\n        <div className=\"relative z-20 mx-auto py-8\">\r\n          <div className=\"p-6\">\r\n            <h3 className=\"mb-1 text-lg font-bold text-gray-900 dark:text-gray-100\">\r\n              Build with Us\r\n            </h3>\r\n            <p className=\"text-gray-700 dark:text-gray-300\">\r\n              Start building your next project with our pre-built components.\r\n              Let&apos; build MVPs fast\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/cards/basic/dot-card.tsx", "target": "/components/mvpblocks/dot-card.tsx"}]}