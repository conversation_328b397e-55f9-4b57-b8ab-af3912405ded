{"name": "delete-project", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/dialog.json", "https://blocks.mvp-subha.me/r/input.json", "https://blocks.mvp-subha.me/r/label.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport { useId, useState } from \"react\";\r\nimport { CircleAlertIcon } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from \"@/components/ui/dialog\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\n\r\nconst PROJECT_NAME = \"Mvpblocks\";\r\n\r\nexport default function DeleteProject() {\r\n  const id = useId();\r\n  const [inputValue, setInputValue] = useState(\"\");\r\n\r\n  return (\r\n    <Dialog>\r\n      <DialogTrigger asChild>\r\n        <Button variant=\"destructive\">Delete project</Button>\r\n      </DialogTrigger>\r\n      <DialogContent>\r\n        <div className=\"flex flex-col items-center gap-2\">\r\n          <div\r\n            className=\"flex size-9 shrink-0 items-center justify-center rounded-full border\"\r\n            aria-hidden=\"true\"\r\n          >\r\n            <CircleAlertIcon className=\"opacity-80\" size={16} />\r\n          </div>\r\n          <DialogHeader>\r\n            <DialogTitle className=\"sm:text-center\">\r\n              Final confirmation\r\n            </DialogTitle>\r\n            <DialogDescription className=\"sm:text-center\">\r\n              This action cannot be undone. To confirm, please enter the project\r\n              name <span className=\"text-primary\">{PROJECT_NAME}</span>.\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n        </div>\r\n\r\n        <form className=\"space-y-5\">\r\n          <div className=\"*:not-first:mt-2\">\r\n            <Label htmlFor={id}>Project name</Label>\r\n            <Input\r\n              id={id}\r\n              type=\"text\"\r\n              placeholder={`Type ${PROJECT_NAME} to confirm`}\r\n              value={inputValue}\r\n              onChange={(e) => setInputValue(e.target.value)}\r\n            />\r\n          </div>\r\n          <DialogFooter>\r\n            <DialogClose asChild>\r\n              <Button type=\"button\" variant=\"outline\" className=\"flex-1\">\r\n                Cancel\r\n              </Button>\r\n            </DialogClose>\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"destructive\"\r\n              className=\"flex-1\"\r\n              disabled={inputValue !== PROJECT_NAME}\r\n            >\r\n              Delete\r\n            </Button>\r\n          </DialogFooter>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/basics/modals/delete-project.tsx", "target": "/components/mvpblocks/delete-project.tsx"}]}