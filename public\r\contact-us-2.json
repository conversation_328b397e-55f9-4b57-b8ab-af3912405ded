{"name": "contact-us-2", "type": "registry:block", "author": "ParnaRoyChowdhury777", "dependencies": ["lucide-react", "react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "\"use client\";\r\nimport React from \"react\";\r\nimport { Mail } from \"lucide-react\";\r\nimport { Phone } from \"lucide-react\";\r\nimport { MapPin } from \"lucide-react\";\r\nimport { Github } from \"lucide-react\";\r\nimport { Twitter } from \"lucide-react\";\r\nimport { Facebook } from \"lucide-react\";\r\nimport { Instagram } from \"lucide-react\";\r\nimport { Send } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\n\r\nexport default function ContactUs2() {\r\n  const [state, setState] = React.useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    message: \"\",\r\n    errors: {} as Record<string, string>,\r\n    submitting: false,\r\n    submitted: false,\r\n  });\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setState({ ...state, submitting: true });\r\n\r\n    // Console log only\r\n    console.log(\"Form submitted:\", {\r\n      name: state.name,\r\n      email: state.email,\r\n      message: state.message,\r\n    });\r\n\r\n    setState({\r\n      ...state,\r\n      submitting: false,\r\n      submitted: true,\r\n    });\r\n  };\r\n\r\n  return (\r\n    <section className=\"w-full max-w-screen-md px-2\">\r\n      <h2 className=\"mb-5 mt-4 bg-gradient-to-br from-gray-300 via-blue-300 to-gray-700 bg-clip-text text-center text-4xl font-bold text-transparent md:text-6xl\">\r\n        Let&apos;s Get in Touch\r\n      </h2>\r\n      <p className=\"mb-6 text-center text-muted-foreground\">\r\n        Fill out the form below and we&apos;ll get back to you as soon as\r\n        possible.\r\n      </p>\r\n      <div\r\n        className=\"mx-auto mb-6 grid w-full items-start gap-12 rounded-lg border bg-white bg-opacity-10 px-4 pb-6 pt-10 shadow shadow-slate-800 md:grid-cols-2 lg:px-12\"\r\n        style={{\r\n          backgroundImage:\r\n            \"radial-gradient(164.75% 100% at 50% 0,#272f3c 0,#0b1224 48.73%)\",\r\n        }}\r\n      >\r\n        <form className=\"space-y-8 text-slate-300\" onSubmit={handleSubmit}>\r\n          <div className=\"space-y-4 text-lg\">\r\n            <label htmlFor=\"name\" />\r\n            Name\r\n            <input\r\n              id=\"name\"\r\n              type=\"text\"\r\n              required\r\n              className=\"flex bg-slate-950 focus:border-slate-500 focus:outline-none hover:outline-none outline-none h-10 w-full rounded-md border border-slate-700 bg-background px-3 py-2 text-sm shadow-inner shadow-slate-800 hover:border-slate-600 hover:transition-all\"\r\n              placeholder=\"Enter your name\"\r\n              name=\"name\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"space-y-4 text-lg\">\r\n            <label htmlFor=\"email\" /> Email\r\n            <input\r\n              id=\"email\"\r\n              placeholder=\"Enter your email\"\r\n              type=\"email\"\r\n              className=\"bg-slate-950 focus:border-slate-500 focus:outline-none hover:outline-none outline-none hover:transition-al flex h-10 w-full rounded-md border border-slate-700 bg-background px-3 py-2 text-sm shadow-inner shadow-slate-800 file:text-sm file:font-medium placeholder:text-muted-foreground hover:border-slate-400 disabled:cursor-not-allowed disabled:opacity-50\"\r\n              name=\"email\"\r\n              required\r\n            />\r\n            {state.errors && state.errors.email && (\r\n              <p className=\"mt-1 text-sm text-red-500\">{state.errors.email}</p>\r\n            )}\r\n          </div>\r\n          <div className=\"space-y-4 text-lg\">\r\n            <label htmlFor=\"message\" className=\"text-lg\" />\r\n            Message\r\n            <textarea\r\n              className=\"bg-slate-950 focus:border-slate-500 focus:outline-none hover:outline-none outline-none mb-5 flex min-h-[100px] w-full rounded-md border border-slate-700 bg-background px-3 py-2 text-sm text-white shadow-inner shadow-slate-800 ring-offset-background placeholder:text-muted-foreground hover:border-slate-400 hover:transition-all disabled:cursor-not-allowed disabled:opacity-50\"\r\n              id=\"message\"\r\n              placeholder=\"Enter your message\"\r\n              name=\"message\"\r\n            />\r\n            {state.errors && (state.errors as any).message && (\r\n              <p className=\"mt-1 text-sm text-red-500\">\r\n                {(state.errors as any).message}\r\n              </p>\r\n            )}\r\n          </div>\r\n          <button\r\n            className=\"group/btn relative block h-10 w-full rounded-md bg-gradient-to-br from-slate-800 to-slate-700 py-2 text-center font-medium text-white shadow-[0px_1px_0px_0px_var(--zinc-800)_inset,0px_-1px_0px_0px_var(--zinc-800)_inset] transition-all duration-300 ease-in-out hover:from-slate-700 hover:to-slate-800 hover:shadow-[0px_1px_0px_0px_var(--zinc-800)_inset,0px_-1px_0px_0px_var(--zinc-800)_inset]\"\r\n            type=\"submit\"\r\n            disabled={state.submitting}\r\n          >\r\n            {state.submitting ? \"Sending...\" : \"Send\"}\r\n            <Send className=\"mx-2 inline h-4\" />\r\n          </button>\r\n        </form>\r\n        <div>\r\n          <h3 className=\"mb-10 text-2xl font-semibold text-slate-300\">\r\n            Connect with Us\r\n          </h3>\r\n          <div className=\"mb-12 flex gap-8\">\r\n            <Link\r\n              className=\"flex h-10 w-10 items-center justify-center rounded-full border border-slate-600 shadow-inner shadow-gray-800 hover:shadow-md hover:shadow-slate-500 hover:transition hover:duration-300 hover:ease-in-out\"\r\n              href=\"#\"\r\n            >\r\n              <Mail className=\"h-5 w-5 text-white\" />\r\n            </Link>\r\n            <div className=\"text-md text-slate-300\">\r\n              <p>Email to us at </p>\r\n              <p><EMAIL></p>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"mb-12 flex gap-8\">\r\n            <Link\r\n              className=\"flex h-10 w-10 items-center justify-center rounded-full border border-slate-600 shadow-inner shadow-gray-800 hover:shadow-md hover:shadow-slate-500 hover:transition hover:duration-300 hover:ease-in-out\"\r\n              href=\"#\"\r\n            >\r\n              <Phone className=\"h-5 w-5 text-white\" />\r\n            </Link>\r\n            <div className=\"text-md text-slate-300\">\r\n              <p>Call us at </p>\r\n              <p>XXXXX XXXXX</p>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"mb-12 flex gap-8\">\r\n            <Link\r\n              className=\"flex h-10 w-10 items-center justify-center rounded-full border border-slate-600 px-2 shadow-inner shadow-gray-800 hover:shadow-md hover:shadow-slate-500 hover:transition hover:duration-300 hover:ease-in-out\"\r\n              href=\"#\"\r\n            >\r\n              <MapPin className=\"h-5 w-5 text-white\" />\r\n            </Link>\r\n            <div className=\"text-md text-slate-300\">\r\n              <p>Location at </p>\r\n              <p>Techno Main Salt Lake, Sector-V, Kolkata-700091</p>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex space-x-12 py-7\">\r\n            <Link\r\n              className=\"flex h-10 w-10 items-center justify-center rounded-full border border-slate-600 bg-gray-800 hover:shadow-md hover:shadow-slate-500 hover:transition hover:duration-300 hover:ease-in-out\"\r\n              href=\"#\"\r\n            >\r\n              <Twitter className=\"h-5 w-5 text-white\" />\r\n            </Link>\r\n            <Link\r\n              className=\"flex h-10 w-10 items-center justify-center rounded-full border border-slate-600 bg-gray-800 hover:shadow-md hover:shadow-slate-500 hover:transition hover:duration-300 hover:ease-in-out\"\r\n              href=\"#\"\r\n            >\r\n              <Facebook className=\"h-5 w-5 text-white\" />\r\n            </Link>\r\n            <Link\r\n              className=\"flex h-10 w-10 items-center justify-center rounded-full border border-slate-700 bg-gray-800 hover:shadow-md hover:shadow-slate-500 hover:transition hover:duration-300 hover:ease-in-out\"\r\n              href=\"#\"\r\n            >\r\n              <Instagram className=\"h-5 w-5 text-white\" />\r\n            </Link>\r\n            <Link\r\n              className=\"flex h-10 w-10 items-center justify-center rounded-full border border-slate-700 bg-gray-800 hover:shadow-md hover:shadow-slate-500 hover:transition hover:duration-300 hover:ease-in-out\"\r\n              href=\"#\"\r\n            >\r\n              <Github className=\"h-5 w-5 text-white\" />\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/contact/contact-us-2.tsx", "target": "/components/mvpblocks/contact-us-2.tsx"}]}