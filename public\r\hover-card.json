{"name": "hover-card", "type": "registry:ui", "dependencies": ["@radix-ui/react-hover-card"], "files": [{"type": "registry:ui", "content": "\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as HoverCardPrimitive from \"@radix-ui/react-hover-card\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst HoverCard = HoverCardPrimitive.Root;\r\n\r\nconst HoverCardTrigger = HoverCardPrimitive.Trigger;\r\n\r\nconst HoverCardContent = React.forwardRef<\r\n  React.ElementRef<typeof HoverCardPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof HoverCardPrimitive.Content>\r\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\r\n  <HoverCardPrimitive.Content\r\n    ref={ref}\r\n    align={align}\r\n    sideOffset={sideOffset}\r\n    className={cn(\r\n      \"z-50 w-64 origin-[--radix-hover-card-content-transform-origin] rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nHoverCardContent.displayName = HoverCardPrimitive.Content.displayName;\r\n\r\nexport { HoverCard, HoverCardTrigger, HoverCardContent };\r\n", "path": "/components/ui/hover-card.tsx", "target": "/components/ui/hover-card.tsx"}]}