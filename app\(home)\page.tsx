"use client";

import Features from "@/components/home/<USER>";
import Hero from "@/components/home/<USER>";
import dynamic from "next/dynamic";

const Gallery = dynamic(() => import("@/components/home/<USER>"), {
  ssr: false,
});

const Testimonials = dynamic(() => import("@/components/home/<USER>"), {
  ssr: false,
});
const CTA = dynamic(() => import("@/components/shared/cta"), {
  ssr: false,
});
const Faqs = dynamic(() => import("@/components/shared/faq"));

export default function Homepage() {
  return (
    <>
      <Hero />
      <Features />
      <Gallery />
      <Testimonials />
      <CTA />
      <Faqs />
    </>
  );
}
