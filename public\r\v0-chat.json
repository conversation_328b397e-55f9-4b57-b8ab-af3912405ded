{"name": "v0-chat", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/use-auto-resize-textarea.json", "https://blocks.mvp-subha.me/r/textarea.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAutoResizeTextarea } from \"@/hooks/use-auto-resize-textarea\";\r\nimport {\r\n  ImageIcon,\r\n  FileUp,\r\n  Figma,\r\n  MonitorIcon,\r\n  CircleUserRound,\r\n  ArrowUpIcon,\r\n  Paperclip,\r\n  PlusIcon,\r\n} from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nexport function VercelV0Chat() {\r\n  const [value, setValue] = useState(\"\");\r\n  const { textareaRef, adjustHeight } = useAutoResizeTextarea({\r\n    minHeight: 60,\r\n    maxHeight: 200,\r\n  });\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {\r\n    if (e.key === \"Enter\" && !e.shiftKey) {\r\n      e.preventDefault();\r\n      if (value.trim()) {\r\n        setValue(\"\");\r\n        adjustHeight(true);\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"mx-auto flex w-full max-w-4xl flex-col items-center space-y-4 p-4 py-24 sm:space-y-8\">\r\n      <h1 className=\"text-center text-2xl font-bold text-foreground sm:text-4xl\">\r\n        What can I help you ship?\r\n      </h1>\r\n\r\n      <div className=\"w-full\">\r\n        <div className=\"relative rounded-xl border border-border bg-secondary/20\">\r\n          <div className=\"overflow-y-auto\">\r\n            <Textarea\r\n              ref={textareaRef}\r\n              value={value}\r\n              onChange={(e) => {\r\n                setValue(e.target.value);\r\n                adjustHeight();\r\n              }}\r\n              onKeyDown={handleKeyDown}\r\n              placeholder=\"Ask v0 a question...\"\r\n              className={cn(\r\n                \"w-full px-4 py-3\",\r\n                \"resize-none\",\r\n                \"bg-transparent\",\r\n                \"border-none\",\r\n                \"text-sm\",\r\n                \"focus:outline-none\",\r\n                \"focus-visible:ring-0 focus-visible:ring-offset-0\",\r\n                \"placeholder:text-sm\",\r\n                \"min-h-[60px]\",\r\n              )}\r\n              style={{\r\n                overflow: \"hidden\",\r\n              }}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-between p-3\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <Button\r\n                type=\"button\"\r\n                size=\"sm\"\r\n                variant=\"outline\"\r\n                className=\"group flex items-center gap-1 rounded-lg p-2 hover:bg-secondary/50\"\r\n              >\r\n                <Paperclip className=\"h-4 w-4\" />\r\n                <span className=\"hidden text-xs transition-opacity group-hover:inline\">\r\n                  Attach\r\n                </span>\r\n              </Button>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <Button\r\n                type=\"button\"\r\n                size=\"sm\"\r\n                variant=\"secondary\"\r\n                className=\"flex items-center justify-between gap-1 rounded-lg border border-dashed border-border px-2 py-1 text-sm transition-colors\"\r\n              >\r\n                <PlusIcon className=\"h-4 w-4\" />\r\n                Project\r\n              </Button>\r\n              <button\r\n                type=\"button\"\r\n                className={cn(\r\n                  \"flex items-center justify-between gap-1 rounded-lg border border-border px-1.5 py-1.5 text-sm transition-colors\",\r\n                  value.trim() ? \"bg-white text-black\" : \"text-zinc-400\",\r\n                )}\r\n              >\r\n                <ArrowUpIcon\r\n                  className={cn(\r\n                    \"h-4 w-4\",\r\n                    value.trim() ? \"text-black\" : \"text-zinc-400\",\r\n                  )}\r\n                />\r\n                <span className=\"sr-only\">Send</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"-mx-4 mt-4 px-4 sm:mx-0 sm:px-0\">\r\n          <div className=\"flex flex-col flex-wrap items-start gap-2 sm:flex-row sm:items-center sm:justify-center sm:gap-3 sm:overflow-x-auto sm:pb-2\">\r\n            <ActionButton\r\n              icon={<ImageIcon className=\"h-4 w-4\" />}\r\n              label=\"Clone a Screenshot\"\r\n            />\r\n            <ActionButton\r\n              icon={<Figma className=\"h-4 w-4\" />}\r\n              label=\"Import from Figma\"\r\n            />\r\n            <ActionButton\r\n              icon={<FileUp className=\"h-4 w-4\" />}\r\n              label=\"Upload a Project\"\r\n            />\r\n            <ActionButton\r\n              icon={<MonitorIcon className=\"h-4 w-4\" />}\r\n              label=\"Landing Page\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\ninterface ActionButtonProps {\r\n  icon: React.ReactNode;\r\n  label: string;\r\n}\r\n\r\nfunction ActionButton({ icon, label }: ActionButtonProps) {\r\n  return (\r\n    <Button\r\n      type=\"button\"\r\n      variant=\"secondary\"\r\n      className=\"flex w-full flex-shrink-0 items-center gap-2 whitespace-nowrap rounded-full border border-border bg-secondary/20 px-3 py-2 transition-colors sm:w-auto sm:px-4\"\r\n    >\r\n      {icon}\r\n      <span className=\"text-xs\">{label}</span>\r\n    </Button>\r\n  );\r\n}\r\n\r\nexport default VercelV0Chat;\r\n", "path": "/components/mvpblocks/chatbot-ui/v0-chat.tsx", "target": "/components/mvpblocks/v0-chat.tsx"}]}