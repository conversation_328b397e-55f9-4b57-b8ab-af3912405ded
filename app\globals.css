@tailwind base;
@tailwind components;
@tailwind utilities;

/* Scrollbar styles moved to scrollbar.css */

::selection {
  background-color: hsl(var(--primary));
  color: #fff;
  text-shadow:
    0 0 1px #fff,
    0 0 1px #fff;
}

::-moz-selection {
  background-color: hsl(var(--primary));
  color: #fff;
  text-shadow:
    0 0 1px #fff,
    0 0 1px #fff;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 349 72.35% 52.76%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 346.8 77.2% 49.8%;
    --radius: 0.5rem;
    --chart-1: 347 77% 50%;
    --chart-2: 352 83% 91%;
    --chart-3: 350 80% 72%;
    --chart-4: 351 83% 82%;
    --chart-5: 349 77% 62%;
    --chart-6: 348 72% 52%;
    --chart-7: 347 77% 50%;
    --chart-8: 352 83% 91%;
    --fd-primary: 349 72.35% 52.76%;
    --fd-primary-foreground: 355.7 100% 97.3%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 20 14.3% 4.1%;
    --foreground: 0 0% 95%;
    --card: 24 9.8% 10%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 95%;
    --primary: 349 72.35% 52.76%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 15%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 12 6.5% 15.1%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 346.8 77.2% 49.8%;
    --chart-1: 347 77% 50%;
    --chart-2: 352 83% 91%;
    --chart-3: 350 80% 72%;
    --chart-4: 351 83% 82%;
    --chart-5: 349 77% 62%;
    --chart-6: 348 72% 52%;
    --chart-7: 347 77% 50%;
    --chart-8: 352 83% 91%;
    --fd-primary: 349 72.35% 52.76%;
    --fd-primary-foreground: 355.7 100% 97.3%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

pre,
code {
  font-size: 16px !important;
  line-height: 1.6rem !important;
}

@keyframes tile {

  0%,
  12.5%,
  100% {
    opacity: 1;
  }

  25%,
  82.5% {
    opacity: 0;
  }
}

.animate-tile {
  animation: tile 2s ease-in-out infinite;
}

.bg-radial {
  --tw-gradient-position: in oklab;
  background-image: radial-gradient(var(--tw-gradient-stops));
}

.fade-left-lg {
  mask-image: linear-gradient(90deg, #0000 15%, #000);
}

.fade-top-lg {
  mask-image: linear-gradient(#0000 15%, #000);
}

.glass:where(.dark, .dark *) {
  display: flex;
  justify-content: center !important;
  align-items: center !important;
  border-radius: 16px !important;
  background: radial-gradient(circle at center,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(30, 0, 0, 0.1) 60%,
      #2a0e0e 100%) !important;

  border: 1px solid rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(2px) !important;
  align-items: center !important;
  justify-content: center !important;
}

.glass:where(.dark, .dark *).rose {
  background: radial-gradient(circle at center,
      rgba(255, 200, 220, 0.08) 0%,
      rgba(80, 10, 40, 0.2) 40%,
      hsl(340, 30%, 10%) 100%) !important;
  box-shadow:
    inset 0 2px 4px rgba(255, 255, 255, 0.08),
    inset 0 -4px 6px rgba(0, 0, 0, 0.6),
    0 3px 8px rgba(230, 10, 100, 0.3),
    0 0 1px rgba(255, 100, 150, 0.15),
    0 0 8px rgba(255, 100, 150, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.04) !important;
  border-radius: 16px !important;
  backdrop-filter: blur(4px) saturate(130%) !important;
  transition: all 0.3s ease !important;
}

.glass:where(.dark, .dark *).rose::after {
  content: "" !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  width: 80px !important;
  height: 80px !important;
  background: radial-gradient(circle,
      rgba(181, 162, 168, 0.3) 0%,
      transparent 70%) !important;
  transform: translate(-50%, -50%) !important;
  border-radius: 50% !important;
  filter: blur(12px) !important;
  pointer-events: none !important;
  z-index: 0 !important;
}

.glass {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16px;
  background: radial-gradient(circle at center,
      rgba(255, 255, 255, 0.6) 0%,
      rgba(255, 220, 230, 0.3) 60%,
      #f9f2f4 100%);
  border: 1px solid rgba(255, 150, 180, 0.1);
  backdrop-filter: blur(3px) saturate(180%);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.glass.rose {
  background: radial-gradient(circle at center,
      rgba(255, 220, 235, 0.3) 0%,
      rgba(255, 190, 210, 0.15) 40%,
      #fef4f7 100%);
  box-shadow:
    inset 0 2px 4px rgba(255, 200, 220, 0.2),
    inset 0 -4px 6px rgba(255, 180, 200, 0.3),
    0 3px 8px rgba(255, 150, 180, 0.2),
    0 0 8px rgba(255, 170, 200, 0.1);
  border: 1px solid rgba(255, 150, 180, 0.15);
  backdrop-filter: blur(5px) saturate(160%);
  position: relative;
  overflow: hidden;
}

.glass.rose::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle,
      rgba(255, 160, 190, 0.3) 0%,
      transparent 70%);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  filter: blur(10px);
  pointer-events: none;
  z-index: 0;
}

@keyframes marquee {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(calc(-100% - var(--gap)));
  }
}

.shiki .highlighted {
  background: hsl(var(--secondary)/50%);
}

.spin {
  animation: spin 5s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* NOOO */
/* Add glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sidebar badge styles */
.sidebar-badge {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  pointer-events: none;
}

/* Make sure badges don't overlap with text */
[data-sidebar="menu-button"] {
  position: relative;
  padding-right: 70px !important;
}

[data-radix-scroll-area-viewport]>div {
  display: block !important;
}

.glass2 {
  border-radius: 16px;
  background: radial-gradient(circle,
      #00000000 0%,
      #ff9e9e1a 60%,
      #ffb9d1 100%) !important;
  border: 1px solid #ffffff0d;
  backdrop-filter: blur(2px);
  justify-content: center;
  transition: all 0.3s;
}

.prose :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  word-break: break-all;
}

.fadein-blur {
  animation: fadein-blur 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s both;
  animation-delay: 1s;
}

@keyframes fadein-blur {
  0% {
    opacity: 0;
    filter: blur(16px);
  }

  100% {
    opacity: 1;
    filter: blur(0);
  }
}

.neumorphic-button::after {
  content: '';
  @apply absolute inset-0 opacity-0 transition-opacity duration-300 bg-gradient-to-br from-[#9b87f5]/20 to-transparent rounded-full;
}

.neumorphic-button:hover::after {
  @apply opacity-100;
}

/* Markdown CSS */
/* Headings */
.markdown-body h1 {
  font-size: 2.5rem; /* 40px */
}

.markdown-body h2 {
  font-size: 2rem; /* 32px */
}

.markdown-body h3 {
  font-size: 1.75rem; /* 28px */
}

.markdown-body h4 {
  font-size: 1.5rem; /* 24px */
}

.markdown-body h5 {
  font-size: 1.25rem; /* 20px */
}

.markdown-body h6 {
  font-size: 1rem; /* 16px */
}

/* Headings */
.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  font-weight: 600;
  line-height: 1.15;
  color: #fff0e8;
  margin-top: 15px;
  margin-bottom: 10px;
}

/* Paragraphs */
.markdown-body p {
  margin-bottom: 10px;
}

/* Links */
.markdown-body a {
  @apply text-rose-500;
  text-decoration: none;
}

.markdown-body a:hover {
  text-decoration: underline;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 20px;
}
.markdown-body ul li {
  list-style-type: disc;
}

.markdown-body blockquote {
  border-left: 4px solid #955d73;
  margin-left: 0;
  margin-right: 0;
  padding-left: 15px;
  color: #a33d73;
}

.markdown-body pre {
  background-color: #464646dc;
  border-left: 3px solid #e60a64;
  color: white;
  padding: 10px;
  overflow-x: auto;
}

.markdown-body code {
  padding: 2px 4px;
  font-size: 90%;
  border-radius: 3px;
}

/* Tables */
.markdown-body table {
  width: 100%;
  border-collapse: collapse;
}

.markdown-body th,
.markdown-body td {
  border: 1px solid hsl(0, 0%, 32%);
  padding: 8px;
  text-align: left;
}

.markdown-body th {
  background-color: #626161;
}
