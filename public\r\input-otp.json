{"name": "input-otp", "type": "registry:ui", "dependencies": ["input-otp"], "files": [{"type": "registry:ui", "content": "\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { OTPInput, OTPInputContext } from \"input-otp\";\r\nimport { Dot } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst InputOTP = React.forwardRef<\r\n  React.ElementRef<typeof OTPInput>,\r\n  React.ComponentPropsWithoutRef<typeof OTPInput>\r\n>(({ className, containerClassName, ...props }, ref) => (\r\n  <OTPInput\r\n    ref={ref}\r\n    containerClassName={cn(\r\n      \"flex items-center gap-2 has-[:disabled]:opacity-50\",\r\n      containerClassName,\r\n    )}\r\n    className={cn(\"disabled:cursor-not-allowed\", className)}\r\n    {...props}\r\n  />\r\n));\r\nInputOTP.displayName = \"InputOTP\";\r\n\r\nconst InputOTPGroup = React.forwardRef<\r\n  React.ElementRef<\"div\">,\r\n  React.ComponentPropsWithoutRef<\"div\">\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"flex items-center\", className)} {...props} />\r\n));\r\nInputOTPGroup.displayName = \"InputOTPGroup\";\r\n\r\nconst InputOTPSlot = React.forwardRef<\r\n  React.ElementRef<\"div\">,\r\n  React.ComponentPropsWithoutRef<\"div\"> & { index: number }\r\n>(({ index, className, ...props }, ref) => {\r\n  const inputOTPContext = React.useContext(OTPInputContext);\r\n  const { char, hasFakeCaret, isActive } = inputOTPContext.slots[index];\r\n\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative flex h-10 w-10 items-center justify-center border-y border-r border-input text-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md\",\r\n        isActive && \"z-10 ring-2 ring-ring ring-offset-background\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      {char}\r\n      {hasFakeCaret && (\r\n        <div className=\"pointer-events-none absolute inset-0 flex items-center justify-center\">\r\n          <div className=\"animate-caret-blink h-4 w-px bg-foreground duration-1000\" />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\nInputOTPSlot.displayName = \"InputOTPSlot\";\r\n\r\nconst InputOTPSeparator = React.forwardRef<\r\n  React.ElementRef<\"div\">,\r\n  React.ComponentPropsWithoutRef<\"div\">\r\n>(({ ...props }, ref) => (\r\n  <div ref={ref} role=\"separator\" {...props}>\r\n    <Dot />\r\n  </div>\r\n));\r\nInputOTPSeparator.displayName = \"InputOTPSeparator\";\r\n\r\nexport { InputOTP, InputOTPGroup, InputOTPSlot, InputOTPSeparator };\r\n", "path": "/components/ui/input-otp.tsx", "target": "/components/ui/input-otp.tsx"}]}