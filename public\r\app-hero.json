{"name": "app-hero", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  ArrowRight,\r\n  Database,\r\n  Sparkles,\r\n  Zap,\r\n  ArrowUpRight,\r\n} from \"lucide-react\";\r\n\r\nexport default function AppHero() {\r\n  // State for animated counters\r\n  const [stats, setStats] = useState({\r\n    users: 0,\r\n    transactions: 0,\r\n    networks: 0,\r\n  });\r\n\r\n  // Animation to count up numbers\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      setStats((prev) => {\r\n        const newUsers = prev.users >= 20000 ? 20000 : prev.users + 500;\r\n        const newTransactions =\r\n          prev.transactions >= 1500000 ? 1500000 : prev.transactions + 37500;\r\n        const newNetworks = prev.networks >= 40 ? 40 : prev.networks + 1;\r\n\r\n        if (\r\n          newUsers === 20000 &&\r\n          newTransactions === 1500000 &&\r\n          newNetworks === 40\r\n        ) {\r\n          clearInterval(interval);\r\n        }\r\n\r\n        return {\r\n          users: newUsers,\r\n          transactions: newTransactions,\r\n          networks: newNetworks,\r\n        };\r\n      });\r\n    }, 50);\r\n\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n        delayChildren: 0.3,\r\n      },\r\n    },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { y: 20, opacity: 0 },\r\n    visible: {\r\n      y: 0,\r\n      opacity: 1,\r\n      transition: { type: \"spring\", stiffness: 100 },\r\n    },\r\n  };\r\n\r\n  // Floating animation for the cube\r\n  const floatingAnimation = {\r\n    y: [0, -10, 0],\r\n    transition: {\r\n      duration: 4,\r\n      repeat: Infinity,\r\n      ease: \"easeInOut\",\r\n    },\r\n  };\r\n\r\n  // Rotation animation for the orbital ring\r\n  const rotateAnimation = {\r\n    rotate: 360,\r\n    transition: {\r\n      duration: 20,\r\n      repeat: Infinity,\r\n      ease: \"linear\",\r\n    },\r\n  };\r\n\r\n  // Glowing effect animation\r\n  const glowAnimation = {\r\n    opacity: [0.5, 0.8, 0.5],\r\n    scale: [1, 1.05, 1],\r\n    transition: {\r\n      duration: 3,\r\n      repeat: Infinity,\r\n      ease: \"easeInOut\",\r\n    },\r\n  };\r\n\r\n  // Tooltip animation\r\n  const tooltipVariants = {\r\n    hidden: { opacity: 0, scale: 0.8 },\r\n    visible: {\r\n      opacity: 1,\r\n      scale: 1,\r\n      transition: {\r\n        type: \"spring\",\r\n        stiffness: 100,\r\n        delay: 1.2,\r\n      },\r\n    },\r\n  };\r\n\r\n  // Badge pulse animation\r\n  const badgePulse = {\r\n    scale: [1, 1.05, 1],\r\n    opacity: [0.9, 1, 0.9],\r\n    transition: {\r\n      duration: 2,\r\n      repeat: Infinity,\r\n      ease: \"easeInOut\",\r\n    },\r\n  };\r\n\r\n  return (\r\n    <section className=\"relative flex min-h-screen w-full flex-col items-center overflow-hidden bg-black py-16 text-white sm:px-6 lg:px-8 lg:py-2\">\r\n      <div className=\"absolute inset-0 z-0 h-full w-full rotate-180 items-center px-5 py-24 opacity-80 [background:radial-gradient(125%_125%_at_50%_10%,#000_40%,#63e_100%)]\"></div>\r\n      <svg\r\n        id=\"noice\"\r\n        className=\"absolute inset-0 z-10 h-full w-full opacity-30\"\r\n      >\r\n        <filter id=\"noise-filter\">\r\n          <feTurbulence\r\n            type=\"fractalNoise\"\r\n            baseFrequency=\"1.34\"\r\n            numOctaves=\"4\"\r\n            stitchTiles=\"stitch\"\r\n          ></feTurbulence>\r\n          <feColorMatrix type=\"saturate\" values=\"0\"></feColorMatrix>\r\n          <feComponentTransfer>\r\n            <feFuncR type=\"linear\" slope=\"0.46\"></feFuncR>\r\n            <feFuncG type=\"linear\" slope=\"0.46\"></feFuncG>\r\n            <feFuncB type=\"linear\" slope=\"0.47\"></feFuncB>\r\n            <feFuncA type=\"linear\" slope=\"0.37\"></feFuncA>\r\n          </feComponentTransfer>\r\n          <feComponentTransfer>\r\n            <feFuncR type=\"linear\" slope=\"1.47\" intercept=\"-0.23\" />\r\n            <feFuncG type=\"linear\" slope=\"1.47\" intercept=\"-0.23\" />\r\n            <feFuncB type=\"linear\" slope=\"1.47\" intercept=\"-0.23\" />\r\n          </feComponentTransfer>\r\n        </filter>\r\n        <rect width=\"100%\" height=\"100%\" filter=\"url(#noise-filter)\"></rect>\r\n      </svg>\r\n      {/* Background effects */}\r\n      <div className=\"absolute inset-0 z-0\">\r\n        {/* Radial gradient */}\r\n        <div className=\"absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-indigo-900/30 via-black/70 to-gray-950 blur-3xl\"></div>\r\n\r\n        {/* Grid pattern */}\r\n        <div className=\"absolute inset-0 opacity-10\">\r\n          <div className=\"h-full w-full bg-[linear-gradient(to_right,rgba(255,255,255,0.22)_1px,transparent_1px),linear-gradient(to_bottom,rgba(255,255,255,0.2)_1px,transparent_1px)] bg-[size:4rem_4rem]\"></div>\r\n        </div>\r\n\r\n        {/* Enhanced glow spots */}\r\n        <div className=\"absolute -left-20 top-20 h-60 w-60 rounded-full bg-purple-600/20 blur-[100px]\"></div>\r\n        <div className=\"absolute -right-20 bottom-20 h-60 w-60 rounded-full bg-blue-600/20 blur-[100px]\"></div>\r\n        <motion.div\r\n          animate={glowAnimation}\r\n          className=\"absolute left-1/4 top-1/3 h-40 w-40 rounded-full bg-indigo-500/10 blur-[80px]\"\r\n        ></motion.div>\r\n        <motion.div\r\n          animate={glowAnimation}\r\n          className=\"absolute bottom-1/3 right-1/4 h-40 w-40 rounded-full bg-purple-500/10 blur-[80px]\"\r\n        ></motion.div>\r\n\r\n        {/* Particle effects - subtle dots */}\r\n        <div className=\"absolute inset-0 opacity-20\">\r\n          {Array.from({ length: 20 }).map((_, i) => (\r\n            <motion.div\r\n              key={i}\r\n              className=\"absolute h-1 w-1 rounded-full bg-white\"\r\n              style={{\r\n                top: `${Math.random() * 100}%`,\r\n                left: `${Math.random() * 100}%`,\r\n              }}\r\n              animate={{\r\n                opacity: [0.2, 0.8, 0.2],\r\n                scale: [1, 1.5, 1],\r\n              }}\r\n              transition={{\r\n                duration: 3 + Math.random() * 2,\r\n                repeat: Infinity,\r\n                ease: \"easeInOut\",\r\n                delay: Math.random() * 2,\r\n              }}\r\n            />\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"fadein-blur relative z-0 mx-auto mb-10 h-[300px] w-[300px] lg:absolute lg:right-1/2 lg:top-1/2 lg:mx-0 lg:mb-0 lg:h-[500px] lg:w-[500px] lg:-translate-y-2/3 lg:translate-x-1/2\">\r\n        <img\r\n          src=\"https://blocks.mvp-subha.me/Adobe Express - file(1).png\"\r\n          alt=\"Nexus Platform 3D Visualization\"\r\n          className=\"h-full w-full object-contain drop-shadow-[0_0_35px_#3358ea85] transition-all duration-1000 hover:scale-110\"\r\n        />\r\n        <motion.div\r\n          variants={tooltipVariants}\r\n          className=\"absolute -left-4 top-4 rounded-lg border border-purple-500/30 bg-black/80 p-2 backdrop-blur-md lg:-left-20 lg:top-1/4\"\r\n        >\r\n          <div className=\"flex items-center gap-2\">\r\n            <Zap className=\"h-4 w-4 text-purple-400\" />\r\n            <span className=\"text-xs font-medium text-purple-200\">\r\n              High Performance\r\n            </span>\r\n          </div>\r\n        </motion.div>\r\n\r\n        <motion.div\r\n          variants={tooltipVariants}\r\n          className=\"absolute -right-4 top-1/2 rounded-lg border border-blue-500/30 bg-black/80 p-2 backdrop-blur-md lg:-right-24\"\r\n        >\r\n          <div className=\"flex items-center gap-2\">\r\n            <Database className=\"h-4 w-4 text-blue-400\" />\r\n            <span className=\"text-xs font-medium text-blue-200\">\r\n              Decentralized Storage\r\n            </span>\r\n          </div>\r\n        </motion.div>\r\n\r\n        <motion.div\r\n          variants={tooltipVariants}\r\n          className=\"absolute bottom-4 left-4 rounded-lg border border-indigo-500/30 bg-black/80 p-2 backdrop-blur-md lg:bottom-1/4 lg:left-8\"\r\n        >\r\n          <div className=\"flex items-center gap-2\">\r\n            <Sparkles className=\"h-4 w-4 text-indigo-400\" />\r\n            <span className=\"text-xs font-medium text-indigo-200\">\r\n              AI-Powered\r\n            </span>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* Main Content Area */}\r\n      <motion.main\r\n        variants={containerVariants}\r\n        initial=\"hidden\"\r\n        animate=\"visible\"\r\n        className=\"relative z-10 mb-10 flex w-full max-w-[1450px] flex-grow flex-col items-center justify-center px-4 text-center sm:px-8 lg:mb-0 lg:items-start lg:justify-end lg:text-left\"\r\n      >\r\n        <motion.div className=\"flex w-full flex-col items-center justify-between lg:flex-row lg:items-start\">\r\n          <div className=\"w-full lg:w-auto\">\r\n            <motion.div\r\n              variants={itemVariants}\r\n              className=\"mb-4 inline-flex items-center rounded-full border border-purple-500/30 bg-purple-500/10 px-3 py-1 text-sm text-purple-300\"\r\n            >\r\n              <span className=\"mr-2 rounded-full bg-purple-500 px-2 py-0.5 text-xs font-semibold text-white\">\r\n                New\r\n              </span>\r\n              Introducing Nexus Platform\r\n            </motion.div>\r\n\r\n            <motion.h1\r\n              variants={itemVariants}\r\n              className=\"mb-6 bg-gradient-to-r from-white/70 via-white to-slate-500/80 bg-clip-text text-3xl leading-tight text-transparent sm:text-4xl md:text-5xl lg:text-6xl\"\r\n            >\r\n              The Bridge Between <br className=\"hidden sm:inline\" />\r\n              <span className=\"bg-gradient-to-r from-purple-400 via-blue-400 to-purple-400 bg-clip-text text-transparent\">\r\n                AI and Web3\r\n              </span>\r\n            </motion.h1>\r\n\r\n            {/* Animated Stats Row */}\r\n            <motion.div\r\n              variants={itemVariants}\r\n              className=\"mb-6 flex flex-wrap justify-center gap-4 md:gap-6 lg:justify-start\"\r\n            >\r\n              <div className=\"rounded-lg border border-purple-500/20 bg-black/40 px-4 py-2 backdrop-blur-sm\">\r\n                <p className=\"text-2xl font-bold text-white\">\r\n                  {stats.users.toLocaleString()}+\r\n                </p>\r\n                <p className=\"text-xs text-gray-400\">Active Users</p>\r\n              </div>\r\n              <div className=\"rounded-lg border border-blue-500/20 bg-black/40 px-4 py-2 backdrop-blur-sm\">\r\n                <p className=\"text-2xl font-bold text-white\">\r\n                  {stats.transactions.toLocaleString()}+\r\n                </p>\r\n                <p className=\"text-xs text-gray-400\">Transactions</p>\r\n              </div>\r\n              <div className=\"rounded-lg border border-indigo-500/20 bg-black/40 px-4 py-2 backdrop-blur-sm\">\r\n                <p className=\"text-2xl font-bold text-white\">\r\n                  {stats.networks}+\r\n                </p>\r\n                <p className=\"text-xs text-gray-400\">Networks</p>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Integration badges */}\r\n            <motion.div\r\n              variants={itemVariants}\r\n              className=\"mb-8 flex flex-wrap items-center justify-center gap-2 lg:justify-start\"\r\n            >\r\n              <span className=\"text-xs font-medium text-gray-400\">\r\n                Integrates with:\r\n              </span>\r\n              <div className=\"flex cursor-pointer items-center gap-2 rounded-full border border-slate-800 bg-slate-900/60 px-2 py-1 text-xs font-medium text-slate-300 backdrop-blur-sm transition-all hover:bg-purple-950\">\r\n                <div className=\"h-2 w-2 rounded-full bg-blue-400\"></div>\r\n                Ethereum\r\n              </div>\r\n              <div className=\"flex cursor-pointer items-center gap-2 rounded-full border border-slate-800 bg-slate-900/60 px-2 py-1 text-xs font-medium text-slate-300 backdrop-blur-sm transition-all hover:bg-purple-950\">\r\n                <div className=\"h-2 w-2 rounded-full bg-purple-400\"></div>\r\n                Solana\r\n              </div>\r\n              <div className=\"flex cursor-pointer items-center gap-2 rounded-full border border-slate-800 bg-slate-900/60 px-2 py-1 text-xs font-medium text-slate-300 backdrop-blur-sm transition-all hover:bg-purple-950\">\r\n                <div className=\"h-2 w-2 rounded-full bg-green-400\"></div>\r\n                OpenAI\r\n              </div>\r\n              <div className=\"flex cursor-pointer items-center gap-2 rounded-full border border-slate-800 bg-slate-900/60 px-2 py-1 text-xs font-medium text-slate-300 backdrop-blur-sm transition-all hover:bg-purple-950\">\r\n                <div className=\"h-2 w-2 rounded-full bg-yellow-400\"></div>\r\n                +5 more\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n\r\n          <div className=\"mt-6 flex flex-col items-center lg:mt-0 lg:items-end\">\r\n            <motion.p\r\n              variants={itemVariants}\r\n              className=\"mb-8 max-w-md px-6 text-center text-lg leading-relaxed text-slate-300/90 lg:text-end\"\r\n            >\r\n              Nexus connects AI tools with Web3 infrastructure, giving\r\n              developers the power to build beyond limits. One platform. Endless\r\n              potential.\r\n            </motion.p>\r\n            <motion.div\r\n              variants={itemVariants}\r\n              className=\"mb-8 flex flex-col flex-wrap gap-4 sm:flex-row lg:justify-end\"\r\n            >\r\n              <Button\r\n                className=\"group rounded-full border-t border-purple-400 bg-gradient-to-b from-purple-700 to-slate-950/80 px-6 py-6 text-white shadow-lg shadow-purple-600/20 transition-all hover:shadow-purple-600/40\"\r\n                size=\"lg\"\r\n              >\r\n                Start Building\r\n                <ArrowRight className=\"ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1\" />\r\n              </Button>\r\n\r\n              <Button\r\n                variant=\"outline\"\r\n                className=\"rounded-full border-purple-500/30 bg-transparent text-white hover:bg-purple-500/10 hover:text-white\"\r\n                size=\"lg\"\r\n              >\r\n                View Demo\r\n              </Button>\r\n            </motion.div>\r\n\r\n            {/* Social proof */}\r\n            <motion.div\r\n              variants={itemVariants}\r\n              className=\"mx-auto flex items-center gap-3 rounded-full border border-slate-800 bg-slate-900/50 px-3 py-1 backdrop-blur-sm lg:mx-0 lg:ml-auto\"\r\n            >\r\n              <div className=\"flex -space-x-2\">\r\n                {[1, 2, 3, 4].map((i) => (\r\n                  <div\r\n                    key={i}\r\n                    className=\"h-6 w-6 overflow-hidden rounded-full border-2 border-slate-900 bg-slate-800\"\r\n                  >\r\n                    <div className=\"h-full w-full bg-gradient-to-br from-purple-500 to-blue-600 opacity-80\"></div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              <span className=\"text-xs text-slate-300\">\r\n                <span className=\"font-semibold text-white\">500+</span>{\" \"}\r\n                developers already building\r\n              </span>\r\n              <ArrowUpRight className=\"h-3 w-3 text-purple-400\" />\r\n            </motion.div>\r\n          </div>\r\n        </motion.div>\r\n      </motion.main>\r\n      <div className=\"absolute -bottom-40 left-1/2 right-auto h-96 w-20 -translate-x-1/2 -rotate-45 rounded-full bg-gray-200/30 blur-[80px] lg:left-auto lg:right-96 lg:translate-x-0\"></div>\r\n      <div className=\"absolute -bottom-52 left-1/2 right-auto h-96 w-20 -translate-x-1/2 -rotate-45 rounded-full bg-gray-300/20 blur-[80px] lg:left-auto lg:right-auto lg:translate-x-0\"></div>\r\n      <div className=\"absolute -bottom-60 left-1/2 right-auto h-96 w-10 -translate-x-20 -rotate-45 rounded-full bg-gray-300/20 blur-[80px] lg:left-auto lg:right-96 lg:-translate-x-40\"></div>\r\n    </section>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/hero/app-hero.tsx", "target": "/components/mvpblocks/app-hero.tsx"}]}