{"name": "congusted-pricing", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react", "@number-flow/react", "canvas-confetti"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/label.json", "https://blocks.mvp-subha.me/r/switch.json", "https://blocks.mvp-subha.me/r/use-media-query.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport { buttonVariants } from \"@/components/ui/button\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { useMediaQuery } from \"@/hooks/use-media-query\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Check, Star } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { useState, useRef } from \"react\";\r\nimport confetti from \"canvas-confetti\";\r\nimport NumberFlow from \"@number-flow/react\";\r\n\r\n// Define your plans\r\nconst plans = [\r\n  {\r\n    name: \"STARTER\",\r\n    price: \"50\",\r\n    yearlyPrice: \"40\",\r\n    period: \"per month\",\r\n    features: [\r\n      \"Up to 10 projects\",\r\n      \"Basic analytics\",\r\n      \"48-hour support response time\",\r\n      \"Limited API access\",\r\n      \"Community support\",\r\n    ],\r\n    description: \"Perfect for individuals and small projects\",\r\n    buttonText: \"Start Free Trial\",\r\n    href: \"/sign-up\",\r\n    isPopular: false,\r\n  },\r\n  {\r\n    name: \"PROFESSIONAL\",\r\n    price: \"99\",\r\n    yearlyPrice: \"79\",\r\n    period: \"per month\",\r\n    features: [\r\n      \"Unlimited projects\",\r\n      \"Advanced analytics\",\r\n      \"24-hour support response time\",\r\n      \"Full API access\",\r\n      \"Priority support\",\r\n      \"Team collaboration\",\r\n      \"Custom integrations\",\r\n    ],\r\n    description: \"Ideal for growing teams and businesses\",\r\n    buttonText: \"Get Started\",\r\n    href: \"/sign-up\",\r\n    isPopular: true,\r\n  },\r\n  {\r\n    name: \"ENTERPRISE\",\r\n    price: \"299\",\r\n    yearlyPrice: \"239\",\r\n    period: \"per month\",\r\n    features: [\r\n      \"Everything in Professional\",\r\n      \"Custom solutions\",\r\n      \"Dedicated account manager\",\r\n      \"1-hour support response time\",\r\n      \"SSO Authentication\",\r\n      \"Advanced security\",\r\n      \"Custom contracts\",\r\n      \"SLA agreement\",\r\n    ],\r\n    description: \"For large organizations with specific needs\",\r\n    buttonText: \"Contact Sales\",\r\n    href: \"/contact\",\r\n    isPopular: false,\r\n  },\r\n];\r\n\r\ninterface PricingPlan {\r\n  name: string;\r\n  price: string;\r\n  yearlyPrice: string;\r\n  period: string;\r\n  features: string[];\r\n  description: string;\r\n  buttonText: string;\r\n  href: string;\r\n  isPopular: boolean;\r\n}\r\n\r\ninterface PricingProps {\r\n  plans: PricingPlan[];\r\n  title?: string;\r\n  description?: string;\r\n}\r\n\r\nexport default function CongestedPricing() {\r\n  const [isMonthly, setIsMonthly] = useState(true);\r\n  const isDesktop = useMediaQuery(\"(min-width: 768px)\");\r\n  const switchRef = useRef<HTMLButtonElement>(null);\r\n\r\n  const handleToggle = (checked: boolean) => {\r\n    setIsMonthly(!checked);\r\n    if (checked && switchRef.current) {\r\n      const rect = switchRef.current.getBoundingClientRect();\r\n      const x = rect.left + rect.width / 2;\r\n      const y = rect.top + rect.height / 2;\r\n\r\n      confetti({\r\n        particleCount: 50,\r\n        spread: 60,\r\n        origin: {\r\n          x: x / window.innerWidth,\r\n          y: y / window.innerHeight,\r\n        },\r\n        colors: [\r\n          \"hsl(var(--primary))\",\r\n          \"hsl(var(--accent))\",\r\n          \"hsl(var(--secondary))\",\r\n          \"hsl(var(--muted))\",\r\n        ],\r\n        ticks: 200,\r\n        gravity: 1.2,\r\n        decay: 0.94,\r\n        startVelocity: 30,\r\n        shapes: [\"circle\"],\r\n      });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container py-20\">\r\n      <div className=\"text-center space-y-4 mb-12\">\r\n        <h2 className=\"text-4xl font-bold tracking-tight sm:text-5xl\">\r\n          Simple, transparent pricing for all.\r\n        </h2>\r\n        <p className=\"text-muted-foreground text-lg whitespace-pre-line\">\r\n          Choose the plan that works for you\\nAll plans include access to our platform, lead generation tools, and dedicated support.\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"flex justify-center mb-10\">\r\n        <label className=\"relative inline-flex items-center cursor-pointer\">\r\n          <Label>\r\n            <Switch\r\n              ref={switchRef as any}\r\n              checked={!isMonthly}\r\n              onCheckedChange={handleToggle}\r\n              className=\"relative\"\r\n            />\r\n          </Label>\r\n        </label>\r\n        <span className=\"ml-2 font-semibold\">\r\n          Annual billing <span className=\"text-primary\">(Save 20%)</span>\r\n        </span>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-3 sm:2 gap-4\">\r\n        {plans.map((plan, index) => (\r\n          <motion.div\r\n            key={index}\r\n            initial={{ y: 50, opacity: 1 }}\r\n            whileInView={\r\n              isDesktop\r\n                ? {\r\n                    y: plan.isPopular ? -20 : 0,\r\n                    opacity: 1,\r\n                    x: index === 2 ? -30 : index === 0 ? 30 : 0,\r\n                    scale: index === 0 || index === 2 ? 0.94 : 1.0,\r\n                  }\r\n                : {}\r\n            }\r\n            viewport={{ once: true }}\r\n            transition={{\r\n              duration: 1.6,\r\n              type: \"spring\",\r\n              stiffness: 100,\r\n              damping: 30,\r\n              delay: 0.4,\r\n              opacity: { duration: 0.5 },\r\n            }}\r\n            className={cn(\r\n              `rounded-2xl border-[1px] p-6 bg-background text-center lg:flex lg:flex-col lg:justify-center relative`,\r\n              plan.isPopular ? \"border-primary border-2\" : \"border-border\",\r\n              \"flex flex-col\",\r\n              !plan.isPopular && \"mt-5\",\r\n              index === 0 || index === 2\r\n                ? \"z-0 transform translate-x-0 translate-y-0 -translate-z-[50px] rotate-y-[10deg]\"\r\n                : \"z-10\",\r\n              index === 0 && \"origin-right\",\r\n              index === 2 && \"origin-left\"\r\n            )}\r\n          >\r\n            {plan.isPopular && (\r\n              <div className=\"absolute top-0 right-0 bg-primary py-0.5 px-2 rounded-bl-xl rounded-tr-xl flex items-center\">\r\n                <Star className=\"text-primary-foreground h-4 w-4 fill-current\" />\r\n                <span className=\"text-primary-foreground ml-1 font-sans font-semibold\">\r\n                  Popular\r\n                </span>\r\n              </div>\r\n            )}\r\n            <div className=\"flex-1 flex flex-col\">\r\n              <p className=\"text-base font-semibold text-muted-foreground\">\r\n                {plan.name}\r\n              </p>\r\n              <div className=\"mt-6 flex items-center justify-center gap-x-2\">\r\n                <span className=\"text-5xl font-bold tracking-tight text-foreground\">\r\n                  <NumberFlow\r\n                    value={\r\n                      isMonthly ? Number(plan.price) : Number(plan.yearlyPrice)\r\n                    }\r\n                    format={{\r\n                      style: \"currency\",\r\n                      currency: \"USD\",\r\n                      minimumFractionDigits: 0,\r\n                      maximumFractionDigits: 0,\r\n                    }}\r\n                    transformTiming={{\r\n                      duration: 500,\r\n                      easing: \"ease-out\",\r\n                    }}\r\n                    willChange\r\n                    className=\"font-variant-numeric: tabular-nums\"\r\n                  />\r\n                </span>\r\n                {plan.period !== \"Next 3 months\" && (\r\n                  <span className=\"text-sm font-semibold leading-6 tracking-wide text-muted-foreground\">\r\n                    / {plan.period}\r\n                  </span>\r\n                )}\r\n              </div>\r\n\r\n              <p className=\"text-xs leading-5 text-muted-foreground\">\r\n                {isMonthly ? \"billed monthly\" : \"billed annually\"}\r\n              </p>\r\n\r\n              <ul className=\"mt-5 gap-2 flex flex-col\">\r\n                {plan.features.map((feature, idx) => (\r\n                  <li key={idx} className=\"flex items-start gap-2\">\r\n                    <Check className=\"h-4 w-4 text-primary mt-1 flex-shrink-0\" />\r\n                    <span className=\"text-left\">{feature}</span>\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n\r\n              <hr className=\"w-full my-4\" />\r\n\r\n              <Link\r\n                href={plan.href}\r\n                className={cn(\r\n                  buttonVariants({\r\n                    variant: \"outline\",\r\n                  }),\r\n                  \"group relative w-full gap-2 overflow-hidden text-lg font-semibold tracking-tighter\",\r\n                  \"transform-gpu ring-offset-current transition-all duration-300 ease-out hover:ring-2 hover:ring-primary hover:ring-offset-1 hover:bg-primary hover:text-primary-foreground\",\r\n                  plan.isPopular\r\n                    ? \"bg-primary text-primary-foreground\"\r\n                    : \"bg-background text-foreground\"\r\n                )}\r\n              >\r\n                {plan.buttonText}\r\n              </Link>\r\n              <p className=\"mt-6 text-xs leading-5 text-muted-foreground\">\r\n                {plan.description}\r\n              </p>\r\n            </div>\r\n          </motion.div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}", "path": "/components/mvpblocks/mainsections/pricing/congusted-pricing.tsx", "target": "/components/mvpblocks/congusted-pricing.tsx"}]}