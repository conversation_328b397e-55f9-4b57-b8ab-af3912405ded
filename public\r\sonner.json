{"name": "sonner", "type": "registry:ui", "dependencies": ["sonner", "next-themes"], "files": [{"type": "registry:ui", "content": "\"use client\";\r\n\r\nimport { useTheme } from \"next-themes\";\r\nimport { Toaster as Sonner } from \"sonner\";\r\n\r\ntype ToasterProps = React.ComponentProps<typeof Sonner>;\r\n\r\nconst Toaster = ({ ...props }: ToasterProps) => {\r\n  const { theme = \"system\" } = useTheme();\r\n\r\n  return (\r\n    <Sonner\r\n      theme={theme as ToasterProps[\"theme\"]}\r\n      className=\"toaster group\"\r\n      toastOptions={{\r\n        classNames: {\r\n          toast:\r\n            \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\r\n          description: \"group-[.toast]:text-muted-foreground\",\r\n          actionButton:\r\n            \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\r\n          cancelButton:\r\n            \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\",\r\n        },\r\n      }}\r\n      {...props}\r\n    />\r\n  );\r\n};\r\n\r\nexport { Toaster };\r\n", "path": "/components/ui/sonner.tsx", "target": "/components/ui/sonner.tsx"}]}