{"name": "phone-mockup", "type": "registry:ui", "dependencies": ["framer-motion", "next-themes", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:ui", "content": "\"use client\";\n\nimport React, { useEffect, useState } from \"react\";\nimport { motion, useAnimation, useMotionValue, useTransform } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\nimport { useTheme } from \"next-themes\";\n\ninterface PhoneMockupProps {\n  className?: string;\n  imageUrl: string;\n  alt?: string;\n  glowColor?: string;\n}\n\nexport default function PhoneMockup({\n  className,\n  imageUrl,\n  alt = \"Mobile screenshot\",\n  glowColor = \"rgba(229, 62, 62, 0.3)\",\n}: PhoneMockupProps) {\n  const { theme } = useTheme();\n  const isDark = theme === \"dark\";\n  const controls = useAnimation();\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [isHovered, setIsHovered] = useState(false);\n\n  const rotateX = useMotionValue(0);\n  const rotateY = useMotionValue(0);\n  const shadowX = useTransform(rotateY, [-15, 0, 15], [-25, 0, 25]);\n  const shadowY = useTransform(rotateX, [-15, 0, 15], [25, 0, -25]);\n\n  useEffect(() => {\n    controls.start({\n      y: [0, -10, 0],\n      transition: {\n        duration: 6,\n        ease: \"easeInOut\",\n        repeat: Infinity,\n      },\n    });\n  }, [controls]);\n\n  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {\n    const rect = e.currentTarget.getBoundingClientRect();\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n\n    const rotateXValue = ((e.clientY - centerY) / (rect.height / 2)) * 15;\n    const rotateYValue = ((e.clientX - centerX) / (rect.width / 2)) * -15;\n\n    rotateX.set(rotateXValue);\n    rotateY.set(rotateYValue);\n    setMousePosition({ x: e.clientX - rect.left, y: e.clientY - rect.top });\n    setIsHovered(true);\n  };\n\n  const handleMouseLeave = () => {\n    rotateX.set(0);\n    rotateY.set(0);\n    setIsHovered(false);\n  };\n\n  return (\n    <motion.div\n      className={cn(\"relative mx-auto w-full max-w-[320px]\", className)}\n      animate={controls}\n      onMouseMove={handleMouseMove}\n      onMouseLeave={handleMouseLeave}\n      style={{\n        transformStyle: \"preserve-3d\",\n        perspective: \"1000px\"\n      }}\n    >\n      <motion.div\n        className=\"absolute -inset-4 rounded-[50px] opacity-60 blur-xl\"\n        animate={{\n          background: isDark\n            ? [\n                `radial-gradient(circle at 30% 30%, rgba(229, 62, 62, ${isHovered ? 0.25 : 0.15}) 0%, rgba(120, 119, 198, ${isHovered ? 0.1 : 0.05}) 50%, transparent 80%)`,\n                `radial-gradient(circle at 70% 70%, rgba(229, 62, 62, ${isHovered ? 0.25 : 0.15}) 0%, rgba(120, 119, 198, ${isHovered ? 0.1 : 0.05}) 50%, transparent 80%)`,\n                `radial-gradient(circle at 30% 30%, rgba(229, 62, 62, ${isHovered ? 0.25 : 0.15}) 0%, rgba(120, 119, 198, ${isHovered ? 0.1 : 0.05}) 50%, transparent 80%)`,\n              ]\n            : [\n                `radial-gradient(circle at 30% 30%, ${isHovered ? glowColor.replace('0.2', '0.3') : glowColor} 0%, transparent 70%)`,\n                `radial-gradient(circle at 70% 70%, ${isHovered ? glowColor.replace('0.2', '0.3') : glowColor} 0%, transparent 70%)`,\n                `radial-gradient(circle at 30% 30%, ${isHovered ? glowColor.replace('0.2', '0.3') : glowColor} 0%, transparent 70%)`,\n              ],\n        }}\n        transition={{\n          duration: isHovered ? 4 : 8,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n        }}\n        style={{\n          zIndex: 0,\n          opacity: isHovered ? 0.8 : 0.6\n        }}\n      />\n\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{\n          duration: 0.8,\n          delay: 0.2,\n          type: \"spring\",\n          stiffness: 300,\n          damping: 20\n        }}\n        className=\"relative z-10 overflow-hidden rounded-[35px] border-[10px] dark:border-foreground/10 border-foreground/5 bg-background shadow-lg\"\n        style={{\n          transformStyle: \"preserve-3d\",\n          perspective: \"1000px\",\n          transform: `rotateX(${rotateX.get()}deg) rotateY(${rotateY.get()}deg) scale(${isHovered ? 1.03 : 1})`,\n          transition: \"transform 0.2s ease-out\",\n          boxShadow: isDark\n            ? `0 0 0 1px rgba(255, 255, 255, 0.05), ${shadowX.get()}px ${shadowY.get()}px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 119, 198, 0.1)`\n            : `0 0 0 1px rgba(229, 62, 62, 0.03), ${shadowX.get()}px ${shadowY.get()}px 40px rgba(0, 0, 0, 0.1), 0 0 20px rgba(229, 62, 62, 0.05)`\n        }}\n      >\n        <div className=\"absolute left-1/2 top-0 z-20 h-7 w-28 -translate-x-1/2 rounded-b-xl bg-foreground/10 backdrop-blur-sm\">\n          <div className=\"absolute left-1/2 top-1/2 h-2 w-2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-foreground/30\"></div>\n        </div>\n\n        <div className=\"absolute -right-[14px] top-16 h-12 w-[4px] rounded-l-sm bg-foreground/20\"></div>\n        <div className=\"absolute -left-[14px] top-16 h-8 w-[4px] rounded-r-sm bg-foreground/20\"></div>\n        <div className=\"absolute -left-[14px] top-28 h-8 w-[4px] rounded-r-sm bg-foreground/20\"></div>\n\n        <div className=\"relative aspect-[9/16] w-full overflow-hidden bg-background\">\n          <motion.img\n            src={imageUrl}\n            alt={alt}\n            className=\"h-full w-full object-cover\"\n            initial={{ opacity: 0, scale: 1.05 }}\n            animate={{\n              opacity: 1,\n              scale: isHovered ? 1.02 : 1,\n            }}\n            transition={{\n              duration: 0.8,\n              delay: 0.4,\n              scale: {\n                type: \"spring\",\n                stiffness: 300,\n                damping: 20\n              }\n            }}\n          />\n          {isHovered && (\n            <motion.div\n              className=\"absolute inset-0\"\n              style={{\n                background: isDark\n                  ? `radial-gradient(circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(255,255,255,0.1) 0%, transparent 60%)`\n                  : `radial-gradient(circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(255,255,255,0.15) 0%, transparent 60%)`,\n                mixBlendMode: \"overlay\",\n              }}\n            />\n          )}\n        </div>\n      </motion.div>\n\n      <motion.div\n        className=\"absolute -right-8 -top-8 h-24 w-24 rounded-full bg-primary/20 blur-xl\"\n        animate={{\n          opacity: [0.4, 0.7, 0.4],\n          scale: [1, 1.1, 1],\n        }}\n        transition={{\n          duration: 4,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n        }}\n      />\n    </motion.div>\n  );\n}\n", "path": "/components/ui/phone-mockup.tsx", "target": "/components/ui/phone-mockup.tsx"}]}