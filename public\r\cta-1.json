{"name": "cta-1", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "import { Globe, Mail, Phone } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\n\r\nexport default function CTA1() {\r\n  return (\r\n    <div className=\"w-full\">\r\n      <section className=\"mx-auto max-w-7xl px-4 py-6 lg:px-8 lg:py-20\">\r\n        <div\r\n          className=\"relative isolate w-full overflow-hidden rounded-2xl\"\r\n          style={{\r\n            background:\r\n              \"linear-gradient(100.5deg,rgba(57,18,241,.4) 29.55%,rgba(164,129,255,.4) 93.8%),radial-gradient(38.35% 93.72% at 18.31% 6.28%,rgba(170,135,252,.8) 0,rgba(61,27,205,.8) 100%)\",\r\n          }}\r\n        >\r\n          <img\r\n            alt=\"bg\"\r\n            loading=\"lazy\"\r\n            width=\"1840\"\r\n            height=\"694\"\r\n            className=\"absolute top-0\"\r\n            src=\"https://blocks.mvp-subha.me/assets/cta/grid.svg\"\r\n          />\r\n          <div className=\"relative isolate overflow-hidden px-4 py-12 sm:px-24\">\r\n            <p className=\"w-fit rounded-xl bg-white px-4 py-1 text-center text-base font-semibold uppercase leading-7 text-black lg:text-left\">\r\n              Get in touch\r\n            </p>\r\n            <h2 className=\"mt-3 max-w-md text-4xl font-semibold text-white md:text-6xl\">\r\n              How Can You <span className=\"text-primary-2\"> Reach Us</span>?\r\n            </h2>\r\n            <p className=\"my-auto mt-3 max-w-2xl text-base text-gray-300 md:text-lg\">\r\n              If you need to get in touch, there are several ways to contact us.\r\n            </p>\r\n            <div className=\"mt-8 flex w-full flex-col justify-between gap-4 text-lg md:flex-row\">\r\n              <a\r\n                className=\"flex items-center gap-2 text-white\"\r\n                href=\"mailto:<EMAIL>\"\r\n              >\r\n                <Mail className=\"h-7 w-7 text-red-500\" />\r\n                <EMAIL>\r\n              </a>\r\n              <a className=\"flex items-center gap-2 text-white\" href=\"#\">\r\n                <Phone className=\"h-7 w-7 text-green-500\" />\r\n                +91-8637373116\r\n              </a>\r\n              <Link className=\"flex items-center gap-2 text-white\" href=\"/\">\r\n                <Globe className=\"h-7 w-7 text-blue-500\" />\r\n                mvp-subha.me\r\n              </Link>\r\n            </div>\r\n            <ul className=\"ml-4 mt-8 list-disc text-sm text-gray-300 md:text-base\">\r\n              <li>Submit your query and state your requirements.</li>\r\n              <li>\r\n                Receive a call back from our experts as per your query to help\r\n                for your need.\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/cta/cta-1.tsx", "target": "/components/mvpblocks/cta-1.tsx"}]}