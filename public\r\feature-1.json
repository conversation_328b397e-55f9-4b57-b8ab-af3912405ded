{"name": "feature-1", "type": "registry:block", "dependencies": [], "registryDependencies": [], "files": [{"type": "registry:block", "content": "import {\r\n  Code,\r\n  Terminal,\r\n  Paintbrush,\r\n  Rocket,\r\n  Book,\r\n  PlusCircle,\r\n} from \"lucide-react\";\r\n\r\nconst features = [\r\n  {\r\n    icon: <Code className=\"h-6 w-6\" />,\r\n    title: \"Developer-Friendly\",\r\n    desc: \"Tailored for developers to create and iterate fast, with minimal overhead and maximum flexibility.\",\r\n  },\r\n  {\r\n    icon: <Terminal className=\"h-6 w-6\" />,\r\n    title: \"CLI Support\",\r\n    desc: \"Command-line interface support for seamless development and workflow integration.\",\r\n  },\r\n  {\r\n    icon: <Paintbrush className=\"h-6 w-6\" />,\r\n    title: \"Easily Customizable\",\r\n    desc: \"Every block is built to be editable. From layout to logic, style to structure—make it your own.\",\r\n  },\r\n  {\r\n    icon: <Rocket className=\"h-6 w-6\" />,\r\n    title: \"v0 Support\",\r\n    desc: \"Launch fast with confidence. Perfect for MVPs, prototypes, and weekend projects.\",\r\n  },\r\n  {\r\n    icon: <Book className=\"h-6 w-6\" />,\r\n    title: \"Full Documentation\",\r\n    desc: \"Comprehensive documentation to understand every feature and maximize your development experience.\",\r\n  },\r\n  {\r\n    icon: <PlusCircle className=\"h-6 w-6\" />,\r\n    title: \"Contribute Yours\",\r\n    desc: \"Add your own blocks to the library and become part of the MVPBlocks community.\",\r\n  },\r\n];\r\nexport default function Feature1() {\r\n  return (\r\n    <section className=\"relative py-14\">\r\n      <div className=\"mx-auto max-w-screen-xl px-4 md:px-8\">\r\n        <div className=\"relative mx-auto max-w-2xl sm:text-center\">\r\n          <div className=\"relative z-10\">\r\n            <h3 className=\"font-geist mt-4 text-3xl font-normal tracking-tighter sm:text-4xl md:text-5xl\">\r\n              Let’s help build your MVP\r\n            </h3>\r\n            <p className=\"font-geist mt-3 text-foreground/60\">\r\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec\r\n              congue, nisl eget molestie varius, enim ex faucibus purus.\r\n            </p>\r\n          </div>\r\n          <div\r\n            className=\"absolute inset-0 mx-auto h-44 max-w-xs blur-[118px]\"\r\n            style={{\r\n              background:\r\n                \"linear-gradient(152.92deg, rgba(192, 15, 102, 0.2) 4.54%, rgba(192, 11, 109, 0.26) 34.2%, rgba(192, 15, 102, 0.1) 77.55%)\",\r\n            }}\r\n          ></div>\r\n        </div>\r\n        <hr className=\"mx-auto mt-5 h-px w-1/2 bg-foreground/30\" />\r\n        <div className=\"relative mt-12\">\r\n          <ul className=\"grid gap-8 sm:grid-cols-2 lg:grid-cols-3\">\r\n            {features.map((item, idx) => (\r\n              <li\r\n                key={idx}\r\n                className=\"transform-gpu space-y-3 rounded-xl border bg-transparent p-4 [box-shadow:0_-20px_80px_-20px_#ff7aa42f_inset]\"\r\n              >\r\n                <div className=\"w-fit transform-gpu rounded-full border p-4 text-primary [box-shadow:0_-20px_80px_-20px_#ff7aa43f_inset] dark:[box-shadow:0_-20px_80px_-20px_#ff7aa40f_inset]\">\r\n                  {item.icon}\r\n                </div>\r\n                <h4 className=\"font-geist text-lg font-bold tracking-tighter\">\r\n                  {item.title}\r\n                </h4>\r\n                <p className=\"text-gray-500\">{item.desc}</p>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/features/feature-1.tsx", "target": "/components/mvpblocks/feature-1.tsx"}]}