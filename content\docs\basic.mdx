---
title: Basic Block
description: All the basic components that are required for any website.
---

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { DrawerCodePreview } from "@/components/preview/drawer-preview";
import { extractSourceCode } from "@/lib/code";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Mouse<PERSON>ointer<PERSON>lick, Loader2, Arrow<PERSON><PERSON> } from "lucide-react";
import Link from "next/link";

## Essential Building Blocks

The Basics category contains fundamental UI components that are essential for any modern website. These components provide the core interactive elements and visual feedback mechanisms that users expect.

<div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-2 mt-6 mb-10">
  <Link href="/docs/basic/buttons" className="no-underline">
  <Card className="group relative overflow-hidden border border-white/10 bg-gradient-to-b from-secondary/60 to-secondary/20 shadow-[0px_2px_10px_0px_rgba(255,255,255,0.1)_inset] hover:shadow-md transition-all duration-300">
    <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_left,transparent_55%,#ffffff10)] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    <CardHeader className="flex flex-row items-start gap-4">
      <div className="p-2 rounded-lg bg-primary/10 text-primary">
        <MousePointerClick className="h-6 w-6" />
      </div>
      <div className="space-y-1 text-left">
        <h3 className="text-2xl m-0 font-semibold tracking-tight">Buttons</h3>
        <p className="text-sm text-muted-foreground">Creative Buttons with built-in styling and smooth animations</p>
      </div>
    </CardHeader>
  </Card>
  </Link>

  <Link href="/docs/basic/loaders" className="no-underline">
  <Card className="group relative overflow-hidden border border-white/10 bg-gradient-to-b from-secondary/60 to-secondary/20 shadow-[0px_2px_10px_0px_rgba(255,255,255,0.1)_inset] hover:shadow-md transition-all duration-300">
    <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_left,transparent_55%,#ffffff10)] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    <CardHeader className="flex flex-row items-start gap-4">
      <div className="p-2 rounded-lg bg-primary/10 text-primary">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
      <div className="space-y-1 text-left">
        <h3 className="text-2xl m-0 font-semibold tracking-tight">Loaders</h3>
        <p className="text-sm text-muted-foreground">Beautiful loading indicators and spinners with animations</p>
      </div>
    </CardHeader>
  </Card>
  </Link>
</div>

## Featured Components

### Buttons

<div className="mb-10 grid gap-5 md:grid-cols-2 xl:grid-cols-2">
  <DrawerCodePreview
    name="star-on-github"
    responsive
    code={(await extractSourceCode("star-on-github")).code}
    lang="tsx"
    classNameComponentContainer="p-16 pt-24"
  />
  <DrawerCodePreview
    name="btn-gradient1"
    responsive
    code={(await extractSourceCode("btn-gradient1")).code}
    lang="tsx"
    classNameComponentContainer="p-16 pt-24"
  />
</div>

### Loaders

<div className="grid gap-5 md:grid-cols-2 xl:grid-cols-2">
  <DrawerCodePreview
    name="classic-loader"
    responsive
    code={(await extractSourceCode("classic-loader")).code}
    lang="tsx"
    classNameComponentContainer="p-16 pt-24"
  />
  <DrawerCodePreview
    name="bouncing-loader"
    responsive
    code={(await extractSourceCode("bouncing-loader")).code}
    lang="tsx"
    classNameComponentContainer="p-16 pt-24"
  />
</div>
