{"name": "geometric-hero", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "\"use client\"\r\n\r\nimport { motion } from \"framer-motion\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { ArrowRight } from \"lucide-react\"\r\nimport { Pacifico } from \"next/font/google\"\r\nimport Image from \"next/image\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst pacifico = Pacifico({\r\n  subsets: [\"latin\"],\r\n  weight: [\"400\"],\r\n  variable: \"--font-pacifico\",\r\n})\r\n\r\nfunction ElegantShape({\r\n  className,\r\n  delay = 0,\r\n  width = 400,\r\n  height = 100,\r\n  rotate = 0,\r\n  gradient = \"from-white/[0.08]\",\r\n}: {\r\n  className?: string\r\n  delay?: number\r\n  width?: number\r\n  height?: number\r\n  rotate?: number\r\n  gradient?: string\r\n}) {\r\n  return (\r\n    <motion.div\r\n      initial={{\r\n        opacity: 0,\r\n        y: -150,\r\n        rotate: rotate - 15,\r\n      }}\r\n      animate={{\r\n        opacity: 1,\r\n        y: 0,\r\n        rotate: rotate,\r\n      }}\r\n      transition={{\r\n        duration: 2.4,\r\n        delay,\r\n        ease: [0.23, 0.86, 0.39, 0.96],\r\n        opacity: { duration: 1.2 },\r\n      }}\r\n      className={cn(\"absolute\", className)}\r\n    >\r\n      <motion.div\r\n        animate={{\r\n          y: [0, 15, 0],\r\n        }}\r\n        transition={{\r\n          duration: 12,\r\n          repeat: Number.POSITIVE_INFINITY,\r\n          ease: \"easeInOut\",\r\n        }}\r\n        style={{\r\n          width,\r\n          height,\r\n        }}\r\n        className=\"relative\"\r\n      >\r\n        <div\r\n          className={cn(\r\n            \"absolute inset-0 rounded-full\",\r\n            \"bg-gradient-to-r to-transparent\",\r\n            gradient,\r\n            \"backdrop-blur-[2px] border-2 border-white/80 dark:border-white/80\",\r\n            \"shadow-[0_8px_32px_0_rgba(255,255,255,0.4)] dark:shadow-[0_8px_32px_0_rgba(255,255,255,0.5)]\",\r\n            \"after:absolute after:inset-0 after:rounded-full\",\r\n            \"after:bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.6),transparent_70%)]\",\r\n            \"dark:after:bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.7),transparent_70%)]\",\r\n          )}\r\n        />\r\n      </motion.div>\r\n    </motion.div>\r\n  )\r\n}\r\n\r\nexport default function HeroGeometric({\r\n  badge = \"MVPBlocks\",\r\n  title1 = \"Build Faster\",\r\n  title2 = \"Ship Sooner\",\r\n}: {\r\n  badge?: string\r\n  title1?: string\r\n  title2?: string\r\n}) {\r\n  const fadeUpVariants = {\r\n    hidden: { opacity: 0, y: 30 },\r\n    visible: (i: number) => ({\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        duration: 1,\r\n        delay: 0.5 + i * 0.2,\r\n        ease: [0.25, 0.4, 0.25, 1],\r\n      },\r\n    }),\r\n  }\r\n\r\n  return (\r\n    <div className=\"relative min-h-screen w-full flex items-center justify-center overflow-hidden bg-background dark:bg-black\">\r\n      <div className=\"absolute inset-0 bg-gradient-to-br from-primary/20 via-transparent to-rose-500/20 dark:from-primary/30 dark:to-rose-500/30 blur-3xl\" />\r\n\r\n      <div className=\"absolute inset-0 overflow-hidden\">\r\n        <ElegantShape\r\n          delay={0.3}\r\n          width={600}\r\n          height={140}\r\n          rotate={12}\r\n          gradient=\"from-indigo-500/70\"\r\n          className=\"left-[-10%] md:left-[-5%] top-[15%] md:top-[20%]\"\r\n        />\r\n\r\n        <ElegantShape\r\n          delay={0.5}\r\n          width={500}\r\n          height={120}\r\n          rotate={-15}\r\n          gradient=\"from-rose-400\"\r\n          className=\"right-[-5%] md:right-[0%] top-[70%] md:top-[75%]\"\r\n        />\r\n\r\n        <ElegantShape\r\n          delay={0.4}\r\n          width={300}\r\n          height={80}\r\n          rotate={-8}\r\n          gradient=\"from-violet-400\"\r\n          className=\"left-[5%] md:left-[10%] bottom-[5%] md:bottom-[10%]\"\r\n        />\r\n\r\n        <ElegantShape\r\n          delay={0.6}\r\n          width={200}\r\n          height={60}\r\n          rotate={20}\r\n          gradient=\"from-amber-500/70 dark:from-amber-400/90\"\r\n          className=\"right-[15%] md:right-[20%] top-[10%] md:top-[15%]\"\r\n        />\r\n\r\n        <ElegantShape\r\n          delay={0.7}\r\n          width={150}\r\n          height={40}\r\n          rotate={-25}\r\n          gradient=\"from-cyan-500/70 dark:from-cyan-400/90\"\r\n          className=\"left-[20%] md:left-[25%] top-[5%] md:top-[10%]\"\r\n        />\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 md:px-6 max-w-6xl\">\r\n        <div className=\"max-w-3xl mx-auto text-center\">\r\n          <motion.div\r\n            custom={0}\r\n            variants={fadeUpVariants}\r\n            initial=\"hidden\"\r\n            animate=\"visible\"\r\n            className=\"inline-flex items-center gap-2 px-4 py-1.5 rounded-full bg-card/50 border border-primary/30 backdrop-blur-sm mb-8 md:mb-12 shadow-sm items-center\"\r\n          >\r\n            <img src=\"/logo.webp\" alt=\"logo\" className=\"h-6 w-6\" />\r\n            <span className=\"text-sm font-medium text-foreground tracking-wide\">{badge}</span>\r\n          </motion.div>\r\n\r\n          <motion.div custom={1} variants={fadeUpVariants} initial=\"hidden\" animate=\"visible\">\r\n            <h1 className=\"text-4xl sm:text-6xl md:text-8xl font-bold mb-6 md:mb-8 tracking-tight mx-4\">\r\n              <span className=\"bg-clip-text text-transparent bg-gradient-to-b from-foreground to-foreground/80\">{title1}</span>\r\n              <br />\r\n              <span\r\n                className={cn(\r\n                  \"bg-clip-text text-transparent bg-gradient-to-r from-primary via-primary/90 to-rose-500 p-4\",\r\n                  pacifico.className,\r\n                  \"font-bold\"\r\n                )}\r\n              >\r\n                {title2}\r\n              </span>\r\n            </h1>\r\n          </motion.div>\r\n\r\n          <motion.div custom={2} variants={fadeUpVariants} initial=\"hidden\" animate=\"visible\">\r\n            <p className=\"text-base sm:text-lg md:text-xl text-muted-foreground mb-10 leading-relaxed max-w-xl mx-auto px-4\">\r\n              Accelerate your development with our modern, accessible, and customizable UI components.\r\n            </p>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            custom={3}\r\n            variants={fadeUpVariants}\r\n            initial=\"hidden\"\r\n            animate=\"visible\"\r\n            className=\"flex flex-col sm:flex-row gap-4 justify-center\"\r\n          >\r\n            <Button\r\n              size=\"lg\"\r\n              className=\"rounded-full bg-gradient-to-r from-primary to-rose-500 hover:from-primary/90 hover:to-rose-500/90 shadow-md shadow-primary/10 border-none\"\r\n            >\r\n              Get Started\r\n              <ArrowRight className=\"ml-2 h-4 w-4\" />\r\n            </Button>\r\n            <Button\r\n              size=\"lg\"\r\n              variant=\"outline\"\r\n              className=\"rounded-full border-primary/30 hover:bg-primary/5 shadow-sm\"\r\n            >\r\n              View Components\r\n            </Button>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"absolute inset-0 bg-gradient-to-t from-background dark:from-black via-transparent to-background/80 dark:to-black/80 pointer-events-none\" />\r\n    </div>\r\n  )\r\n}\r\n", "path": "/components/mvpblocks/mainsections/hero/geometric-hero.tsx", "target": "/components/mvpblocks/geometric-hero.tsx"}]}