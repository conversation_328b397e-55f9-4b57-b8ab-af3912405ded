---
title: Logo Cloud
description: Logo clouds are used to showcase a collection of logos or brands in a visually appealing way. They can be used to highlight partnerships, sponsors, or featured brands.
root: required
---

import {ComponentPreview} from "@/components/preview/component-preview";
import {extractSourceCode} from "@/lib/code";

## Sparkles Logo Cloud

<ComponentPreview
  name="sparkles-logo"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode("sparkles-logo")).code}
  lang="tsx"
  fromDocs={true}
/>
