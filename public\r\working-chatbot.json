{"name": "working-chatbot", "type": "registry:block", "dependencies": ["lucide-react", "react", "react-markdown", "sonner"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/textarea.json", "https://blocks.mvp-subha.me/r/use-auto-resize-textarea.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from \"lucide-react\";\r\nimport { useCallback, useRef, useState } from \"react\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAutoResizeTextarea } from \"@/hooks/use-auto-resize-textarea\";\r\nimport { useChat } from \"ai/react\";\r\nimport Image from \"next/image\";\r\nimport Markdown from \"react-markdown\";\r\nimport { toast } from \"sonner\"\r\n\r\nfunction AiInput({\r\n  value,\r\n  onChange,\r\n  onSubmit,\r\n  onKeyDown,\r\n}: {\r\n  value: string;\r\n  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;\r\n  onSubmit: () => void;\r\n  onKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;\r\n}) {\r\n  const { textareaRef, adjustHeight } = useAutoResizeTextarea({\r\n    minHeight: 50,\r\n    maxHeight: 200,\r\n  });\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      <div className=\"relative mx-auto flex w-full max-w-4xl flex-col items-start gap-2\">\r\n        <div className=\"relative mx-auto w-full max-w-4xl\">\r\n          <Textarea\r\n            ref={textareaRef}\r\n            id=\"ai-input-06\"\r\n            placeholder=\"Ask me anything!\"\r\n            className={cn(\r\n              \"w-full max-w-4xl resize-none text-wrap rounded-3xl border-none bg-muted/50 py-4 pl-6 pr-12 leading-[1.2] text-foreground ring-primary/20 placeholder:text-muted-foreground/70\",\r\n              \"min-h-[56px] transition-all duration-200 focus:ring-2 focus:ring-primary/30\",\r\n            )}\r\n            value={value}\r\n            onKeyDown={onKeyDown}\r\n            onChange={(e) => {\r\n              onChange(e);\r\n              adjustHeight();\r\n            }}\r\n          />\r\n          <button\r\n            onClick={onSubmit}\r\n            className={cn(\r\n              \"absolute right-3 top-1/2 -translate-y-1/2 rounded-xl bg-primary/10 p-2 transition-all duration-200 hover:bg-primary/20\",\r\n              value.trim() ? \"opacity-100\" : \"cursor-not-allowed opacity-50\",\r\n            )}\r\n            type=\"button\"\r\n            disabled={!value.trim()}\r\n          >\r\n            <CornerRightUp\r\n              className={cn(\r\n                \"h-4 w-4 text-primary transition-opacity\",\r\n                value ? \"opacity-100\" : \"opacity-50\",\r\n              )}\r\n            />\r\n          </button>\r\n        </div>\r\n        <p className=\"ml-4 text-xs text-muted-foreground\">\r\n          {value.length}/2000 characters\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default function WorkingChatbot() {\r\n  const [responseTimes, setResponseTimes] = useState<Record<string, number>>(\r\n    {},\r\n  );\r\n  const startTimeRef = useRef<number>(0);\r\n  // Using theme for styling is handled by Tailwind's dark mode classes\r\n\r\n  const {\r\n    messages,\r\n    input,\r\n    handleInputChange,\r\n    handleSubmit: originalHandleSubmit,\r\n    status,\r\n    error,\r\n  } = useChat({\r\n    api: \"/api/demo-chat\",\r\n    onFinish: (message) => {\r\n      const endTime = Date.now();\r\n      const duration = (endTime - startTimeRef.current) / 1000;\r\n      setResponseTimes((prev) => ({\r\n        ...prev,\r\n        [message.id]: duration,\r\n      }));\r\n    },\r\n  });\r\n\r\n  // Check if the AI is currently generating a response\r\n  const isLoading = status === \"submitted\" || status === \"streaming\";\r\n\r\n  const handleSubmit = useCallback(\r\n    (e?: React.FormEvent) => {\r\n      if (!input.trim()) return;\r\n      startTimeRef.current = Date.now();\r\n      originalHandleSubmit(e);\r\n    },\r\n    [originalHandleSubmit, input],\r\n  )\r\n\r\n  const handleKeyDown = useCallback(\r\n    (event: React.KeyboardEvent<HTMLTextAreaElement>) => {\r\n      if (event.key === \"Enter\" && !event.shiftKey) {\r\n        event.preventDefault();\r\n        handleSubmit();\r\n      }\r\n    },\r\n    [handleSubmit],\r\n  )\r\n\r\n  return (\r\n    <div className=\"mx-auto flex h-svh w-full max-w-4xl flex-col pb-0.5\">\r\n      <div className=\"h-full flex-1 overflow-y-auto rounded-xl border border-primary/20 bg-card/40 p-4 text-sm leading-6 text-card-foreground shadow-md sm:text-base sm:leading-7\">\r\n        {messages.length > 0 ? (\r\n          messages.map((m) => {\r\n            return (\r\n              <div key={m.id} className=\"mb-4 whitespace-pre-wrap\">\r\n                {m.role === \"user\" ? (\r\n                  <div className=\"flex flex-row px-2 py-4 sm:px-4\">\r\n                    <img\r\n                      alt=\"user\"\r\n                      className=\"mr-2 flex size-6 rounded-full sm:mr-4 md:size-8\"\r\n                      src=\"/logo.webp\"\r\n                      width={32}\r\n                      height={32}\r\n                    />\r\n                    <div className=\"flex max-w-3xl items-center\">\r\n                      <p>{m.content}</p>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"relative mb-4 flex rounded-xl bg-neutral-50 px-2 py-6 dark:bg-neutral-900 sm:px-4\">\r\n                    <Bot className=\"mr-2 flex text-primary size-8 p-1 bg-secondary rounded-full sm:mr-4\" />{\" \"}\r\n                    <div className=\"markdown-body w-full max-w-3xl overflow-x-auto rounded-xl\">\r\n                      <Markdown>{m.content}</Markdown>\r\n                      {responseTimes[m.id] && (\r\n                        <div className=\"mt-2 text-xs text-neutral-500\">\r\n                          Response time: {responseTimes[m.id].toFixed(3)}s\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                    <button\r\n                      type=\"button\"\r\n                      title=\"copy\"\r\n                      className=\"absolute right-2 top-2 rounded-full bg-rose-500 p-1 opacity-50 transition-all hover:opacity-75 active:scale-95 dark:bg-neutral-800\"\r\n                      onClick={() => {\r\n                        navigator.clipboard.writeText(m.content);\r\n                        toast.success(\"Copied to clipboard\");\r\n                      }}\r\n                    >\r\n                      <Copy className=\"h-4 w-4 text-white\" />\r\n                    </button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            );\r\n          })\r\n        ) : (\r\n          <div className=\"flex h-full flex-col items-center justify-center\">\r\n            <p className=\"mx-auto px-2 text-center text-xl font-semibold tracking-wide text-muted-foreground md:text-2xl\">\r\n              Start Chatting with\r\n              <br />\r\n              <span className=\"text-2xl font-bold text-primary md:text-4xl\">\r\n                MVPBlocks\r\n              </span>\r\n              <span className=\"text-primary\">.AI</span>\r\n            </p>\r\n            <div className=\"group relative mt-6\">\r\n              <div className=\"absolute -inset-1 rounded-full bg-gradient-to-r from-primary/30 to-primary/10 opacity-75 blur-md transition-opacity duration-500 group-hover:opacity-100\"></div>\r\n              <Image\r\n                src=\"/assets/robo.svg\"\r\n                alt=\"AI Assistant\"\r\n                width={250}\r\n                height={250}\r\n                className=\"relative transition-all duration-500 hover:scale-105 active:scale-95\"\r\n              />\r\n            </div>\r\n          </div>\r\n        )}\r\n        {isLoading && (\r\n          <div className=\"mx-auto flex w-fit items-center gap-2 rounded-full bg-primary/5 px-4 py-2\">\r\n            <Sparkles className=\"h-4 w-4 animate-pulse text-primary\" />\r\n            <span className=\"animate-pulse bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-sm font-medium text-transparent\">\r\n              Generating response...\r\n            </span>\r\n          </div>\r\n        )}\r\n        {error && (\r\n          <div className=\"mx-auto w-fit rounded-lg border border-destructive/20 bg-destructive/10 p-3 text-destructive\">\r\n            Something went wrong! Please try again.\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <form className=\"mt-2\" onSubmit={handleSubmit}>\r\n        <div className=\"relative\">\r\n          <AiInput\r\n            value={input}\r\n            onChange={handleInputChange}\r\n            onSubmit={handleSubmit}\r\n            onKeyDown={handleKeyDown}\r\n          />\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/chatbot-ui/working-chatbot.tsx", "target": "/components/mvpblocks/working-chatbot.tsx"}]}