{"name": "feature-2", "type": "registry:block", "dependencies": ["framer-motion", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport Image from \"next/image\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Rocket, Code, Paintbrush } from \"lucide-react\";\r\n\r\nconst features = [\r\n  {\r\n    step: \"Step 1\",\r\n    title: \"Build Faster\",\r\n    content:\r\n      \"Create your MVP in record time with our pre-built blocks and components.\",\r\n    icon: <Rocket className=\"h-6 w-6 text-primary\" />,\r\n    image:\r\n      \"https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e?q=80&w=2070&auto=format&fit=crop\",\r\n  },\r\n  {\r\n    step: \"Step 2\",\r\n    title: \"Customize Easily\",\r\n    content:\r\n      \"Tailor every component to your needs with our intuitive design system and flexible architecture.\",\r\n    icon: <Paintbrush className=\"h-6 w-6 text-primary\" />,\r\n    image:\r\n      \"https://images.unsplash.com/photo-1618761714954-0b8cd0026356?q=80&w=2070&auto=format&fit=crop\",\r\n  },\r\n  {\r\n    step: \"Step 3\",\r\n    title: \"Deploy Confidently\",\r\n    content:\r\n      \"Launch your product with confidence using our optimized, responsive, and accessible components.\",\r\n    icon: <Code className=\"h-6 w-6 text-primary\" />,\r\n    image:\r\n      \"https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2070&auto=format&fit=crop\",\r\n  },\r\n  {\r\n    step: \"Step 4\",\r\n    title: \"Add Yours!\",\r\n    content:\r\n      \"Contribute your own blocks and become part of the MVPBlocks community.\",\r\n    icon: <Code className=\"h-6 w-6 text-primary\" />,\r\n    image:\r\n      \"https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2070&auto=format&fit=crop\",\r\n  },\r\n];\r\n\r\nexport default function FeatureSteps() {\r\n  const [currentFeature, setCurrentFeature] = useState(0);\r\n  const [progress, setProgress] = useState(0);\r\n\r\n  useEffect(() => {\r\n    const timer = setInterval(() => {\r\n      if (progress < 100) {\r\n        setProgress((prev) => prev + 100 / (4000 / 100));\r\n      } else {\r\n        setCurrentFeature((prev) => (prev + 1) % features.length);\r\n        setProgress(0);\r\n      }\r\n    }, 100);\r\n\r\n    return () => clearInterval(timer);\r\n  }, [progress]);\r\n\r\n  return (\r\n    <div className={\"p-8 md:p-12\"}>\r\n      <div className=\"mx-auto w-full max-w-7xl\">\r\n        <div className=\"relative mx-auto mb-12 max-w-2xl sm:text-center\">\r\n          <div className=\"relative z-10\">\r\n            <h2 className=\"font-geist text-3xl font-bold tracking-tighter md:text-4xl lg:text-5xl\">\r\n              Build Your MVP in Three Steps\r\n            </h2>\r\n            <p className=\"font-geist mt-3 text-foreground/60\">\r\n              MVPBlocks helps you create, customize, and deploy your product\r\n              faster than ever before.\r\n            </p>\r\n          </div>\r\n          <div\r\n            className=\"absolute inset-0 mx-auto h-44 max-w-xs blur-[118px]\"\r\n            style={{\r\n              background:\r\n                \"linear-gradient(152.92deg, rgba(192, 15, 102, 0.2) 4.54%, rgba(192, 11, 109, 0.26) 34.2%, rgba(192, 15, 102, 0.1) 77.55%)\",\r\n            }}\r\n          ></div>\r\n        </div>\r\n        <hr className=\"mx-auto mb-10 h-px w-1/2 bg-foreground/30\" />\r\n\r\n        <div className=\"flex flex-col gap-6 md:grid md:grid-cols-2 md:gap-10\">\r\n          <div className=\"order-2 space-y-8 md:order-1\">\r\n            {features.map((feature, index) => (\r\n              <motion.div\r\n                key={index}\r\n                className=\"flex items-center gap-6 md:gap-8\"\r\n                initial={{ opacity: 0.3, x: -20 }}\r\n                animate={{\r\n                  opacity: index === currentFeature ? 1 : 0.3,\r\n                  x: 0,\r\n                  scale: index === currentFeature ? 1.05 : 1,\r\n                }}\r\n                transition={{ duration: 0.5 }}\r\n              >\r\n                <motion.div\r\n                  className={cn(\r\n                    \"flex h-12 w-12 items-center justify-center rounded-full border-2 md:h-14 md:w-14\",\r\n                    index === currentFeature\r\n                      ? \"scale-110 border-primary bg-primary/10 text-primary [box-shadow:0_0_15px_rgba(192,15,102,0.3)]\"\r\n                      : \"border-muted-foreground bg-muted\",\r\n                  )}\r\n                >\r\n                  {feature.icon}\r\n                </motion.div>\r\n\r\n                <div className=\"flex-1\">\r\n                  <h3 className=\"text-xl font-semibold md:text-2xl\">\r\n                    {feature.title}\r\n                  </h3>\r\n                  <p className=\"text-sm text-muted-foreground md:text-base\">\r\n                    {feature.content}\r\n                  </p>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n\r\n          <div\r\n            className={cn(\r\n              \"relative order-1 h-[200px] overflow-hidden rounded-xl border border-primary/20 [box-shadow:0_5px_30px_-15px_rgba(192,15,102,0.3)] md:order-2 md:h-[300px] lg:h-[400px]\",\r\n            )}\r\n          >\r\n            <AnimatePresence mode=\"wait\">\r\n              {features.map(\r\n                (feature, index) =>\r\n                  index === currentFeature && (\r\n                    <motion.div\r\n                      key={index}\r\n                      className=\"absolute inset-0 overflow-hidden rounded-lg\"\r\n                      initial={{ y: 100, opacity: 0, rotateX: -20 }}\r\n                      animate={{ y: 0, opacity: 1, rotateX: 0 }}\r\n                      exit={{ y: -100, opacity: 0, rotateX: 20 }}\r\n                      transition={{ duration: 0.5, ease: \"easeInOut\" }}\r\n                    >\r\n                      <Image\r\n                        src={feature.image}\r\n                        alt={feature.title}\r\n                        className=\"h-full w-full transform object-cover transition-transform hover:scale-105\"\r\n                        width={1000}\r\n                        height={500}\r\n                        priority\r\n                      />\r\n                      <div className=\"absolute bottom-0 left-0 right-0 h-2/3 bg-gradient-to-t from-background via-background/50 to-transparent\" />\r\n\r\n                      <div className=\"absolute bottom-4 left-4 rounded-lg bg-background/80 p-2 backdrop-blur-sm\">\r\n                        <span className=\"text-xs font-medium text-primary\">\r\n                          {feature.step}\r\n                        </span>\r\n                      </div>\r\n                    </motion.div>\r\n                  ),\r\n              )}\r\n            </AnimatePresence>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/features/feature-2.tsx", "target": "/components/mvpblocks/feature-2.tsx"}]}