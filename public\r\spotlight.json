{"name": "spotlight", "type": "registry:ui", "dependencies": ["react"], "registryDependencies": [], "files": [{"type": "registry:ui", "content": "\"use client\";\r\nimport React from \"react\";\r\nimport { motion } from \"motion/react\";\r\n\r\ntype SpotlightProps = {\r\n  gradientFirst?: string;\r\n  gradientSecond?: string;\r\n  gradientThird?: string;\r\n  translateY?: number;\r\n  width?: number;\r\n  height?: number;\r\n  smallWidth?: number;\r\n  duration?: number;\r\n  xOffset?: number;\r\n};\r\n\r\nexport const Spotlight = ({\r\n  gradientFirst = \"radial-gradient(68.54% 68.72% at 55.02% 31.46%, hsla(336, 100%, 50%, 0.1) 0, hsla(341, 100%, 55%, 0.02) 50%, hsla(336, 100%, 45%, 0) 80%)\",\r\n  gradientSecond = \"radial-gradient(50% 50% at 50% 50%, hsla(333, 100%, 85%, 0.06) 0, hsla(335, 100%, 55%, 0.02) 80%, transparent 100%)\",\r\n  gradientThird = \"radial-gradient(50% 50% at 50% 50%, hsla(332, 100%, 85%, 0.04) 0, hsla(327, 100%, 85%, 0.04) 80%, transparent 100%)\",\r\n  translateY = -350,\r\n  width = 560,\r\n  height = 1380,\r\n  smallWidth = 240,\r\n  duration = 7,\r\n  xOffset = 100,\r\n}: SpotlightProps = {}) => {\r\n  return (\r\n    <motion.div\r\n      initial={{\r\n        opacity: 0,\r\n      }}\r\n      animate={{\r\n        opacity: 1,\r\n      }}\r\n      transition={{\r\n        duration: 1.5,\r\n      }}\r\n      className=\"pointer-events-none absolute inset-0 z-10 h-full w-full\"\r\n    >\r\n      <motion.div\r\n        animate={{\r\n          x: [0, xOffset, 0],\r\n        }}\r\n        transition={{\r\n          duration,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n        }}\r\n        className=\"pointer-events-none absolute left-0 top-0 z-0 h-screen w-screen\"\r\n      >\r\n        <div\r\n          style={{\r\n            transform: `translateY(${translateY}px) rotate(-45deg)`,\r\n            background: gradientFirst,\r\n            width: `${width}px`,\r\n            height: `${height}px`,\r\n          }}\r\n          className={`absolute left-0 top-0`}\r\n        />\r\n\r\n        <div\r\n          style={{\r\n            transform: \"rotate(-45deg) translate(5%, -50%)\",\r\n            background: gradientSecond,\r\n            width: `${smallWidth}px`,\r\n            height: `${height}px`,\r\n          }}\r\n          className={`absolute left-0 top-0 origin-top-left`}\r\n        />\r\n\r\n        <div\r\n          style={{\r\n            transform: \"rotate(-45deg) translate(-180%, -70%)\",\r\n            background: gradientThird,\r\n            width: `${smallWidth}px`,\r\n            height: `${height}px`,\r\n          }}\r\n          className={`absolute left-0 top-0 origin-top-left`}\r\n        />\r\n      </motion.div>\r\n\r\n      <motion.div\r\n        animate={{\r\n          x: [0, -xOffset, 0],\r\n        }}\r\n        transition={{\r\n          duration,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          ease: \"easeInOut\",\r\n        }}\r\n        className=\"pointer-events-none absolute right-0 top-0 z-40 h-screen w-screen\"\r\n      >\r\n        <div\r\n          style={{\r\n            transform: `translateY(${translateY}px) rotate(45deg)`,\r\n            background: gradientFirst,\r\n            width: `${width}px`,\r\n            height: `${height}px`,\r\n          }}\r\n          className={`absolute right-0 top-0`}\r\n        />\r\n\r\n        <div\r\n          style={{\r\n            transform: \"rotate(45deg) translate(-5%, -50%)\",\r\n            background: gradientSecond,\r\n            width: `${smallWidth}px`,\r\n            height: `${height}px`,\r\n          }}\r\n          className={`absolute right-0 top-0 origin-top-right`}\r\n        />\r\n\r\n        <div\r\n          style={{\r\n            transform: \"rotate(45deg) translate(180%, -70%)\",\r\n            background: gradientThird,\r\n            width: `${smallWidth}px`,\r\n            height: `${height}px`,\r\n          }}\r\n          className={`absolute right-0 top-0 origin-top-right`}\r\n        />\r\n      </motion.div>\r\n    </motion.div>\r\n  );\r\n};\r\n", "path": "/components/ui/spotlight.tsx", "target": "/components/ui/spotlight.tsx"}]}