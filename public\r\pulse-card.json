{"name": "pulse-card", "type": "registry:ui", "dependencies": ["framer-motion", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:ui", "content": "import React from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface CardProps {\r\n  icon: React.ReactNode;\r\n  title: string;\r\n  description: string;\r\n  className?: string;\r\n  variant?: \"emerald\" | \"blue\" | \"purple\" | \"amber\" | \"rose\";\r\n  size?: \"sm\" | \"md\" | \"lg\";\r\n  glowEffect?: boolean;\r\n  hoverScale?: number;\r\n  interactive?: boolean;\r\n  showGridLines?: boolean;\r\n}\r\n\r\nexport const VARIANTS = {\r\n  emerald: {\r\n    accent: \"rose-500\",\r\n    gradient: \"from-rose-500/20 to-rose-500/0\",\r\n    shine:\r\n      \"205deg, transparent 0deg, hsl(160deg 95% 39%) 20deg, hsl(160deg 100% 85% / 0.3) 280deg\",\r\n    border: \"rose-500/20\",\r\n    color: \"rgb(244 63 94)\",\r\n  },\r\n  blue: {\r\n    accent: \"blue-500\",\r\n    gradient: \"from-blue-500/20 to-blue-500/0\",\r\n    shine:\r\n      \"205deg, transparent 0deg, hsl(220deg 95% 39%) 20deg, hsl(220deg 100% 85% / 0.3) 280deg\",\r\n    border: \"blue-500/20\",\r\n    color: \"rgb(244 63 94)\",\r\n  },\r\n  purple: {\r\n    accent: \"purple-500\",\r\n    gradient: \"from-purple-500/20 to-purple-500/0\",\r\n    shine:\r\n      \"205deg, transparent 0deg, hsl(280deg 95% 39%) 20deg, hsl(280deg 100% 85% / 0.3) 280deg\",\r\n    border: \"purple-500/20\",\r\n    color: \"rgb(244 63 94)\",\r\n  },\r\n  amber: {\r\n    accent: \"amber-500\",\r\n    gradient: \"from-amber-500/20 to-amber-500/0\",\r\n    shine:\r\n      \"205deg, transparent 0deg, hsl(40deg 95% 39%) 20deg, hsl(40deg 100% 85% / 0.3) 280deg\",\r\n    border: \"amber-500/20\",\r\n    color: \"rgb(244 63 94)\",\r\n  },\r\n  rose: {\r\n    accent: \"rose-500\",\r\n    gradient: \"from-rose-500/20 to-rose-500/0\",\r\n    shine:\r\n      \"205deg, transparent 0deg, hsl(340deg 95% 39%) 20deg, hsl(340deg 100% 85% / 0.3) 280deg\",\r\n    border: \"rose-500/20\",\r\n    color: \"rgb(244 63 94)\",\r\n  },\r\n};\r\n\r\nconst SIZES = {\r\n  sm: {\r\n    padding: \"p-6 pt-12\",\r\n    iconSize: \"h-5 w-5\",\r\n    titleSize: \"text-sm\",\r\n    descSize: \"text-xs\",\r\n  },\r\n  md: {\r\n    padding: \"p-8 pt-16\",\r\n    iconSize: \"h-6 w-6\",\r\n    titleSize: \"text-base\",\r\n    descSize: \"text-[15px]\",\r\n  },\r\n  lg: {\r\n    padding: \"p-6 pt-16\",\r\n    iconSize: \"h-7 w-7\",\r\n    titleSize: \"text-lg\",\r\n    descSize: \"text-base\",\r\n  },\r\n};\r\n\r\nexport function CardHoverEffect({\r\n  icon,\r\n  title,\r\n  description,\r\n  className,\r\n  variant = \"emerald\",\r\n  size = \"md\",\r\n  glowEffect = false,\r\n  hoverScale = 1.02,\r\n  interactive = true,\r\n  showGridLines = true,\r\n}: CardProps) {\r\n  const variantConfig = VARIANTS[variant];\r\n  const sizeConfig = SIZES[size];\r\n\r\n  const Div = interactive ? motion.div : \"div\";\r\n  const IconWrapper = interactive ? motion.span : \"span\";\r\n\r\n  return (\r\n    <Div\r\n      whileHover={interactive ? { scale: hoverScale } : undefined}\r\n      transition={{ duration: 0.3, ease: \"easeInOut\", type: \"keyframes\" }}\r\n      className={cn(\r\n        \"group relative z-30 w-full cursor-pointer overflow-hidden rounded-2xl\",\r\n        sizeConfig.padding,\r\n        // Light mode styles\r\n        \"before:bg-linear-to-b bg-white/80 before:from-white/5 before:to-white/20 before:backdrop-blur-3xl\",\r\n        \"after:bg-linear-to-b after:from-transparent after:via-transparent after:to-white/20\",\r\n        // Dark mode styles\r\n        \"dark:before:bg-linear-to-b dark:bg-black/5 dark:before:from-black/5 dark:before:to-black/20\",\r\n        \"dark:after:to-black/20\",\r\n        // Common styles\r\n        \"before:absolute before:inset-0 before:rounded-[inherit] before:content-['']\",\r\n        \"after:absolute after:inset-0 after:rounded-[inherit] after:content-['']\",\r\n        glowEffect && `hover:before:bg-${variantConfig.accent}/10`,\r\n        // Shadows\r\n        \"shadow-[0px_3px_8px_rgba(0,0,0,0.04),0px_12px_20px_rgba(0,0,0,0.08)]\",\r\n        \"hover:shadow-[0px_5px_15px_rgba(0,0,0,0.03),0px_25px_35px_rgba(0,0,0,0.2)]\",\r\n        \"dark:shadow-[0px_3px_8px_rgba(0,0,0,0.08),0px_12px_20px_rgba(0,0,0,0.15)]\",\r\n        \"dark:hover:shadow-[0px_5px_15px_rgba(0,0,0,0.06),0px_25px_35px_rgba(0,0,0,0.4)]\",\r\n        className,\r\n      )}\r\n      style={\r\n        {\r\n          \"--card-color\": variantConfig.color,\r\n        } as React.CSSProperties\r\n      }\r\n    >\r\n      {/* Moving Border */}\r\n      <div\r\n        className=\"absolute inset-0 overflow-hidden rounded-[inherit]\"\r\n        style={{\r\n          mask: \"linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)\",\r\n          maskComposite: \"exclude\",\r\n          padding: \"2px\",\r\n        }}\r\n      >\r\n        <div\r\n          className=\"absolute inset-[-200%] opacity-0 transition-opacity duration-300 group-hover:opacity-100\"\r\n          style={{\r\n            background: `conic-gradient(from 0deg at 50% 50%, transparent 0deg, transparent 340deg, var(--card-color) 360deg)`,\r\n            animation: \"spin 4s linear infinite\",\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {/* Icon */}\r\n      <IconWrapper\r\n        className=\"relative z-50 table rounded-xl pb-2\"\r\n        whileHover={interactive ? { scale: 1.1 } : undefined}\r\n        transition={{ duration: 0.3, ease: \"easeInOut\", type: \"keyframes\" }}\r\n      >\r\n        <span\r\n          className={cn(\r\n            \"absolute inset-[4.5px] rounded-[inherit]\",\r\n            \"bg-linear-to-b from-black/5 to-black/10 backdrop-blur-3xl\",\r\n            \"dark:from-white/10 dark:to-white/5\",\r\n            \"transition-all duration-300\",\r\n          )}\r\n        />\r\n        <span\r\n          className={cn(\r\n            \"z-1 relative block transition-colors duration-300\",\r\n            \"text-black/60 group-hover:text-[var(--card-color)]\",\r\n            \"dark:text-zinc-400\",\r\n            sizeConfig.iconSize,\r\n          )}\r\n        >\r\n          {icon}\r\n        </span>\r\n      </IconWrapper>\r\n\r\n      {/* Content */}\r\n      <div className=\"relative z-30 mt-2\">\r\n        <h3\r\n          className={cn(\r\n            \"font-medium transition-colors duration-300\",\r\n            \"text-black/80 group-hover:text-[var(--card-color)]\",\r\n            \"dark:text-white/80\",\r\n            sizeConfig.titleSize,\r\n          )}\r\n        >\r\n          {title}\r\n        </h3>\r\n        <p\r\n          className={cn(\r\n            \"mt-1 transition-colors duration-300\",\r\n            \"text-black/60\",\r\n            \"dark:text-white/40\",\r\n            sizeConfig.descSize,\r\n          )}\r\n        >\r\n          {description}\r\n        </p>\r\n      </div>\r\n\r\n      {/* Shine Effect */}\r\n      <div className=\"absolute inset-0 z-20 overflow-hidden rounded-[inherit] opacity-100 transition-all duration-500\">\r\n        <div\r\n          className=\"absolute bottom-[55%] left-1/2 aspect-square w-[200%] -translate-x-1/2 rounded-[50%]\"\r\n          style={{\r\n            background: `conic-gradient(from ${variantConfig.shine}, transparent 360deg)`,\r\n            filter: \"blur(40px)\",\r\n          }}\r\n        />\r\n      </div>\r\n    </Div>\r\n  );\r\n}\r\n", "path": "/components/ui/pulse-card.tsx", "target": "/components/ui/pulse-card.tsx"}]}