---
title: Categories
description: All the components are categorized into different categories. You can add your own category and add blocks to it.
icon: Blocks
full: true
---

import SpotlightCard from "@/components/ui/spotlight-cards";

<div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-4">
  <SpotlightCard title="Basics" href="/docs/basic" />
  <SpotlightCard title="Required" href="/docs/required" />
  <SpotlightCard title="MainSections" href="/docs/mainsections" />
  <SpotlightCard title="Forms" href="/docs/forms" />
  <SpotlightCard title="Cards" href="/docs/cards" />
  <SpotlightCard title="Chatbot" href="/docs/chatbot" />
  <SpotlightCard title="Dashboard" href="/docs/dashboard" />
  <SpotlightCard title="Creative" href="/docs/creative" />
  <SpotlightCard title="Grids" href="/docs/grids" />
  <SpotlightCard title="Text Animations" href="/docs/text-animations" />
</div>
