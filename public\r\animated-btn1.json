{"name": "animated-btn1", "type": "registry:block", "dependencies": [], "registryDependencies": [], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nexport default function AnimatedBtn1() {\r\n  return (\r\n    <div className=\"flex items-center justify-center\">\r\n      <button className=\"bubbleeffectbtn\" type=\"button\">\r\n        <style jsx>{`\r\n          .bubbleeffectbtn {\r\n            min-width: 130px;\r\n            height: 40px;\r\n            color: #fff;\r\n            cursor: pointer;\r\n            transition: all 0.3s ease;\r\n            position: relative;\r\n            display: inline-block;\r\n            outline: none;\r\n            border-radius: 25px;\r\n            border: none;\r\n            background: linear-gradient(45deg, #212529, #343a40);\r\n            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\r\n            z-index: 1;\r\n            overflow: hidden;\r\n          }\r\n\r\n          .bubbleeffectbtn:before {\r\n            content: \"\";\r\n            position: absolute;\r\n            top: -50%;\r\n            left: -50%;\r\n            width: 200%;\r\n            height: 200%;\r\n            background: linear-gradient(\r\n              45deg,\r\n              rgba(255, 255, 255, 0.1),\r\n              rgba(255, 255, 255, 0)\r\n            );\r\n            transform: rotate(45deg);\r\n            transition: all 0.5s ease;\r\n            z-index: -1;\r\n          }\r\n\r\n          .bubbleeffectbtn:hover:before {\r\n            top: -100%;\r\n            left: -100%;\r\n          }\r\n\r\n          .bubbleeffectbtn:after {\r\n            border-radius: 25px;\r\n            position: absolute;\r\n            content: \"\";\r\n            width: 0;\r\n            height: 100%;\r\n            top: 0;\r\n            z-index: -1;\r\n            box-shadow:\r\n              inset 2px 2px 2px 0px rgba(255, 255, 255, 0.5),\r\n              7px 7px 20px 0px rgba(0, 0, 0, 0.1),\r\n              4px 4px 5px 0px rgba(0, 0, 0, 0.1);\r\n            transition: all 0.3s ease;\r\n            background: linear-gradient(45deg, #343a40, #495057);\r\n            right: 0;\r\n          }\r\n\r\n          .bubbleeffectbtn:hover:after {\r\n            width: 100%;\r\n            left: 0;\r\n          }\r\n\r\n          .bubbleeffectbtn:active {\r\n            top: 2px;\r\n            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n            background: linear-gradient(45deg, #212529, #343a40);\r\n          }\r\n\r\n          .bubbleeffectbtn span {\r\n            position: relative;\r\n            z-index: 2;\r\n          }\r\n        `}</style>\r\n\r\n        <span className=\"text-sm font-medium\">Hover me</span>\r\n      </button>\r\n    </div>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/basics/buttons/animated-btn1.tsx", "target": "/components/mvpblocks/animated-btn1.tsx"}]}