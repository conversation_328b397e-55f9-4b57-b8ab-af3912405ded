{"name": "ripple-loader", "type": "registry:block", "dependencies": ["framer-motion"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "import { motion } from \"framer-motion\";\r\n\r\nexport default function RippleWaveLoader() {\r\n  return (\r\n    <div className=\"flex items-center justify-center space-x-1\">\r\n      {[...Array(7)].map((_, index) => (\r\n        <motion.div\r\n          key={index}\r\n          className=\"h-8 w-2 rounded-full bg-red-500\"\r\n          animate={{\r\n            scaleY: [0.5, 1.5, 0.5],\r\n            scaleX: [1, 0.8, 1],\r\n            translateY: [\"0%\", \"-15%\", \"0%\"],\r\n          }}\r\n          transition={{\r\n            duration: 1,\r\n            repeat: Infinity,\r\n            ease: \"easeInOut\",\r\n            delay: index * 0.1,\r\n          }}\r\n        />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/basics/loaders/ripple-loader.tsx", "target": "/components/mvpblocks/ripple-loader.tsx"}]}