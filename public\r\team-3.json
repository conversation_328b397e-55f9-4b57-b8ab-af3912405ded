{"name": "team-3", "author": "mosespace", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "import Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { cn } from '@/lib/utils';\r\nimport { Facebook, Linkedin, Twitter } from 'lucide-react';\r\n\r\n// Team member data type\r\ntype TeamMember = {\r\n  id: number;\r\n  name: string;\r\n  role: string;\r\n  image: string;\r\n  troubleMaker?: boolean;\r\n  socialMedia?: {\r\n    facebook?: string;\r\n    twitter?: string;\r\n    linkedin?: string;\r\n  };\r\n};\r\n\r\nconst defaultTeamMembers: TeamMember[] = [\r\n  {\r\n    id: 1,\r\n    name: '<PERSON><PERSON>',\r\n    role: 'CEO & Founder',\r\n    troubleMaker: false,\r\n    image:\r\n      'https://img.freepik.com/free-psd/3d-rendering-avatar_23-2150833554.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\r\n    socialMedia: {\r\n      facebook: '#',\r\n      twitter: '#',\r\n      linkedin: '#',\r\n    },\r\n  },\r\n  {\r\n    id: 2,\r\n    name: '<PERSON>',\r\n    role: 'CTO',\r\n    troubleMaker: true,\r\n\r\n    image:\r\n      'https://img.freepik.com/premium-photo/png-headset-headphones-portrait-cartoon_53876-762197.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\r\n    socialMedia: {\r\n      facebook: '#',\r\n      twitter: '#',\r\n      linkedin: '#',\r\n    },\r\n  },\r\n  {\r\n    id: 3,\r\n    name: '<PERSON>lear',\r\n    role: 'Lead Designer',\r\n    troubleMaker: false,\r\n\r\n    image:\r\n      'https://img.freepik.com/premium-photo/png-cartoon-portrait-glasses-white-background_53876-905385.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\r\n    socialMedia: {\r\n      facebook: '#',\r\n      twitter: '#',\r\n      linkedin: '#',\r\n    },\r\n  },\r\n  {\r\n    id: 4,\r\n    name: 'Michael Brown',\r\n    role: 'Marketing Director',\r\n    troubleMaker: false,\r\n\r\n    image:\r\n      'https://img.freepik.com/premium-psd/3d-avatar-character_975163-690.jpg?ga=GA1.1.1818589012.1736774497&semt=ais_hybrid',\r\n    socialMedia: {\r\n      facebook: '#',\r\n      twitter: '#',\r\n      linkedin: '#',\r\n    },\r\n  },\r\n];\r\n\r\nexport default function Team3({\r\n  teamMembers = defaultTeamMembers,\r\n  backgroundColor = 'bg-indigo-950',\r\n  title = 'top people at each industry',\r\n  headline = 'Partnered with most of the',\r\n}: {\r\n  title?: string;\r\n  headline?: string;\r\n  backgroundColor?: string;\r\n  teamMembers: TeamMember[];\r\n}) {\r\n  return (\r\n    <section className={cn(`${backgroundColor} w-full py-16 text-white`)}>\r\n      <div className=\"container mx-auto px-4\">\r\n        <div className=\"text-center mb-12\">\r\n          <p className=\"text-indigo-400 mb-2\">{headline}</p>\r\n          <h2 className=\"text-3xl font-light mb-6\">\r\n            <span className=\"italic\">{title}</span>\r\n          </h2>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\r\n          {teamMembers.map((member, index) => (\r\n            <div key={member.id} className=\"group relative\">\r\n              <div className=\"relative h-80 w-full overflow-hidden rounded-lg\">\r\n                <div className=\"absolute inset-0  opacity-20 group-hover:opacity-0 transition-opacity z-10\"></div>\r\n                <Image\r\n                  src={member.image || '/placeholder.svg'}\r\n                  alt={member.name}\r\n                  fill\r\n                  className=\"object-cover\"\r\n                />\r\n              </div>\r\n              <div className=\"absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-indigo-900 to-transparent\">\r\n                <h3 className=\"text-xl font-bold\">{member.name}</h3>\r\n                <p className=\"text-indigo-300 mb-3\">{member.role}</p>\r\n                <div className=\"flex space-x-3\">\r\n                  {member.socialMedia?.facebook && (\r\n                    <Link\r\n                      href={member.socialMedia.facebook}\r\n                      className=\"text-white hover:text-indigo-300\"\r\n                    >\r\n                      <Facebook size={18} />\r\n                    </Link>\r\n                  )}\r\n                  {member.socialMedia?.twitter && (\r\n                    <Link\r\n                      href={member.socialMedia.twitter}\r\n                      className=\"text-white hover:text-indigo-300\"\r\n                    >\r\n                      <Twitter size={18} />\r\n                    </Link>\r\n                  )}\r\n                  {member.socialMedia?.linkedin && (\r\n                    <Link\r\n                      href={member.socialMedia.linkedin}\r\n                      className=\"text-white hover:text-indigo-300\"\r\n                    >\r\n                      <Linkedin size={18} />\r\n                    </Link>\r\n                  )}\r\n                </div>\r\n              </div>\r\n              {member.troubleMaker && (\r\n                <div className=\"absolute top-0 left-0 right-0 p-6 bg-indigo-600 bg-opacity-80\">\r\n                  <p className=\"text-sm font-medium\">Trouble Maker</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/team/team-3.tsx", "target": "/components/mvpblocks/team-3.tsx"}]}