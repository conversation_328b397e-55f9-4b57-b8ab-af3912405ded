/* Default scrollbar styles for all pages */
::-webkit-scrollbar {
  width: 4px !important;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(100, 100, 100, 0.5) !important;
  border-radius: 4px !important;
}

::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.1) !important;
}

/* Hide scrollbars for preview page and component-preview */
html.preview-page ::-webkit-scrollbar,
html.preview-page *::-webkit-scrollbar,
.component-preview::-webkit-scrollbar,
.component-preview *::-webkit-scrollbar {
  width: 0 !important;
  display: none !important;
}

/* For Firefox */
html.preview-page,
html.preview-page *,
.component-preview,
.component-preview * {
  scrollbar-width: none !important;
}

/* For other browsers */
html.preview-page,
html.preview-page *,
.component-preview,
.component-preview * {
  -ms-overflow-style: none !important;
}
