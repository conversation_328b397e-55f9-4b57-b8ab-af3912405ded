{"name": "testimonials-marquee", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/marquee.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Star } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { Marquee } from \"@/components/ui/marquee\";\r\n\r\nexport function Highlight({\r\n  children,\r\n  className,\r\n}: {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n}) {\r\n  return (\r\n    <span\r\n      className={cn(\r\n        \"bg-blue-500/10 p-1 py-0.5 font-bold text-blue-500\",\r\n        className,\r\n      )}\r\n    >\r\n      {children}\r\n    </span>\r\n  );\r\n}\r\n\r\nexport interface TestimonialCardProps {\r\n  name: string;\r\n  role: string;\r\n  img?: string;\r\n  description: React.ReactNode;\r\n  className?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nexport function TestimonialCard({\r\n  description,\r\n  name,\r\n  img,\r\n  role,\r\n  className,\r\n  ...props // Capture the rest of the props\r\n}: TestimonialCardProps) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"mb-4 flex w-full cursor-pointer break-inside-avoid flex-col items-center justify-between gap-6 rounded-xl p-4\",\r\n        // theme styles\r\n        \"border border-border bg-card/50 shadow-sm\",\r\n        // hover effect\r\n        \"transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <div className=\"select-none text-sm font-normal text-muted-foreground\">\r\n        {description}\r\n        <div className=\"flex flex-row py-1\">\r\n          <Star className=\"size-4 fill-blue-500 text-blue-500\" />\r\n          <Star className=\"size-4 fill-blue-500 text-blue-500\" />\r\n          <Star className=\"size-4 fill-blue-500 text-blue-500\" />\r\n          <Star className=\"size-4 fill-blue-500 text-blue-500\" />\r\n          <Star className=\"size-4 fill-blue-500 text-blue-500\" />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex w-full select-none items-center justify-start gap-5\">\r\n        <Image\r\n          width={40}\r\n          height={40}\r\n          src={img || \"\"}\r\n          alt={name}\r\n          className=\"size-10 rounded-full ring-1 ring-blue-500/20 ring-offset-2\"\r\n        />\r\n\r\n        <div>\r\n          <p className=\"font-medium text-foreground\">{name}</p>\r\n          <p className=\"text-xs font-normal text-muted-foreground\">{role}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\nconst testimonials = [\r\n  {\r\n    name: \"Jordan Hayes\",\r\n    role: \"CTO at Quantum Innovations\",\r\n    img: \"https://randomuser.me/api/portraits/men/22.jpg\",\r\n    description: (\r\n      <p>\r\n        NexaUI has completely transformed our development workflow.\r\n        <Highlight>\r\n          The component system saved us weeks of custom coding and design work.\r\n        </Highlight>{\" \"}\r\n        Our team can now focus on business logic instead of UI details.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: \"Maya Rodriguez\",\r\n    role: \"Lead Developer at Skyline Digital\",\r\n    img: \"https://randomuser.me/api/portraits/women/33.jpg\",\r\n    description: (\r\n      <p>\r\n        I was skeptical at first, but NexaUI proved me wrong.\r\n        <Highlight>\r\n          The accessibility features and responsive design are top-notch.\r\n        </Highlight>{\" \"}\r\n        It&apos;s rare to find a framework that prioritizes both aesthetics and\r\n        functionality.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: \"Ethan Park\",\r\n    role: \"Startup Founder at Elevate Labs\",\r\n    img: \"https://randomuser.me/api/portraits/men/32.jpg\",\r\n    description: (\r\n      <p>\r\n        As a non-technical founder, NexaUI has been a game-changer for our MVP.\r\n        <Highlight>We launched three months ahead of schedule.</Highlight> The\r\n        modular components allowed us to iterate quickly based on user feedback.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: \"Zoe Bennett\",\r\n    role: \"UX Architect at Fusion Systems\",\r\n    img: \"https://randomuser.me/api/portraits/women/44.jpg\",\r\n    description: (\r\n      <p>\r\n        NexaUI&apos;s attention to detail is impressive.\r\n        <Highlight>\r\n          The micro-interactions and animations create a polished experience.\r\n        </Highlight>{\" \"}\r\n        It&apos;s become our go-to solution for client projects with tight\r\n        deadlines.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: \"Victor Nguyen\",\r\n    role: \"Product Lead at FinEdge\",\r\n    img: \"https://randomuser.me/api/portraits/men/55.jpg\",\r\n    description: (\r\n      <p>\r\n        Our financial dashboard needed a complete overhaul, and NexaUI\r\n        delivered.\r\n        <Highlight>\r\n          The data visualization components are both beautiful and functional.\r\n        </Highlight>{\" \"}\r\n        User engagement has increased by 47% since the redesign.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: \"Amara Johnson\",\r\n    role: \"Frontend Specialist at Nimbus Tech\",\r\n    img: \"https://randomuser.me/api/portraits/women/67.jpg\",\r\n    description: (\r\n      <p>\r\n        The documentation for NexaUI is exceptional.\r\n        <Highlight>\r\n          I was able to implement complex UI patterns in just a few hours.\r\n        </Highlight>{\" \"}\r\n        The TypeScript support is also a major productivity booster.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: \"Leo Tanaka\",\r\n    role: \"Creative Technologist at Prism Agency\",\r\n    img: \"https://randomuser.me/api/portraits/men/78.jpg\",\r\n    description: (\r\n      <p>\r\n        NexaUI has the perfect balance of flexibility and structure.\r\n        <Highlight>\r\n          We can maintain brand consistency while still creating unique\r\n          experiences.\r\n        </Highlight>{\" \"}\r\n        Our clients are consistently impressed with the results.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: \"Sophia Martinez\",\r\n    role: \"E-commerce Director at StyleHub\",\r\n    img: \"https://randomuser.me/api/portraits/women/89.jpg\",\r\n    description: (\r\n      <p>\r\n        Our conversion rates have increased by 28% since implementing NexaUI.\r\n        <Highlight>\r\n          The checkout flow components are optimized for both desktop and\r\n          mobile.\r\n        </Highlight>{\" \"}\r\n        The dark mode support was also a huge hit with our customers.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: \"Aiden Wilson\",\r\n    role: \"Healthcare Solutions Architect\",\r\n    img: \"https://randomuser.me/api/portraits/men/92.jpg\",\r\n    description: (\r\n      <p>\r\n        NexaUI&apos;s accessibility features were crucial for our healthcare\r\n        platform.\r\n        <Highlight>\r\n          We passed compliance requirements with minimal additional work.\r\n        </Highlight>{\" \"}\r\n        The form components are especially well-designed for complex data entry.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: \"Olivia Chen\",\r\n    role: \"EdTech Product Manager at LearnSphere\",\r\n    img: \"https://randomuser.me/api/portraits/women/29.jpg\",\r\n    description: (\r\n      <p>\r\n        Our educational platform needed to work for students of all ages and\r\n        abilities.\r\n        <Highlight>\r\n          NexaUI&apos;s inclusive design principles made this possible without\r\n          compromise.\r\n        </Highlight>{\" \"}\r\n        The interactive components have significantly improved student\r\n        engagement.\r\n      </p>\r\n    ),\r\n  },\r\n];\r\n\r\nexport default function Testimonials() {\r\n  return (\r\n    <section className=\"container relative py-10\">\r\n      {/* Decorative elements */}\r\n      <div className=\"absolute -left-20 top-20 z-10 h-64 w-64 rounded-full bg-blue-500/5 blur-3xl\" />\r\n      <div className=\"absolute -right-20 bottom-20 z-10 h-64 w-64 rounded-full bg-blue-500/5 blur-3xl\" />\r\n\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n      >\r\n        <h2 className=\"mb-4 text-center text-4xl font-bold leading-[1.2] tracking-tighter text-foreground md:text-5xl\">\r\n          What Our Users Are Saying\r\n        </h2>\r\n        <h3 className=\"mx-auto mb-8 max-w-lg text-balance text-center text-lg font-medium tracking-tight text-muted-foreground\">\r\n          Don&apos;t just take our word for it. Here&apos;s what{\" \"}\r\n          <span className=\"bg-gradient-to-r from-blue-500 to-sky-500 bg-clip-text text-transparent\">\r\n            real developers\r\n          </span>{\" \"}\r\n          are saying about{\" \"}\r\n          <span className=\"font-semibold text-blue-500\">NexaUI</span>\r\n        </h3>\r\n      </motion.div>\r\n\r\n      <div className=\"relative mt-6 max-h-screen overflow-hidden\">\r\n        <div className=\"gap-4 md:columns-2 xl:columns-3 2xl:columns-4\">\r\n          {Array(Math.ceil(testimonials.length / 3))\r\n            .fill(0)\r\n            .map((_, i) => (\r\n              <Marquee\r\n                vertical\r\n                key={i}\r\n                className={cn({\r\n                  \"[--duration:60s]\": i === 1,\r\n                  \"[--duration:30s]\": i === 2,\r\n                  \"[--duration:70s]\": i === 3,\r\n                })}\r\n              >\r\n                {testimonials.slice(i * 3, (i + 1) * 3).map((card, idx) => (\r\n                  <motion.div\r\n                    key={idx}\r\n                    initial={{ opacity: 0 }}\r\n                    whileInView={{ opacity: 1 }}\r\n                    viewport={{ once: true }}\r\n                    transition={{\r\n                      delay: Math.random() * 0.8,\r\n                      duration: 1.2,\r\n                    }}\r\n                  >\r\n                    <TestimonialCard {...card} />\r\n                  </motion.div>\r\n                ))}\r\n              </Marquee>\r\n            ))}\r\n        </div>\r\n        <div className=\"pointer-events-none absolute inset-x-0 bottom-0 h-1/4 w-full bg-gradient-to-t from-background from-20%\"></div>\r\n        <div className=\"pointer-events-none absolute inset-x-0 top-0 h-1/4 w-full bg-gradient-to-b from-background from-20%\"></div>\r\n      </div>\r\n    </section>\r\n  );\r\n}", "path": "/components/mvpblocks/mainsections/testimonials/testimonials-marquee.tsx", "target": "/components/mvpblocks/testimonials-marquee.tsx"}]}