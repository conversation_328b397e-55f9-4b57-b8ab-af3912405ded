{"name": "toggle", "type": "registry:ui", "dependencies": ["@radix-ui/react-toggle"], "files": [{"type": "registry:ui", "content": "\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as TogglePrimitive from \"@radix-ui/react-toggle\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst toggleVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 gap-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-transparent\",\r\n        outline:\r\n          \"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-3 min-w-10\",\r\n        sm: \"h-9 px-2.5 min-w-9\",\r\n        lg: \"h-11 px-5 min-w-11\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nconst Toggle = React.forwardRef<\r\n  React.ElementRef<typeof TogglePrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof TogglePrimitive.Root> &\r\n    VariantProps<typeof toggleVariants>\r\n>(({ className, variant, size, ...props }, ref) => (\r\n  <TogglePrimitive.Root\r\n    ref={ref}\r\n    className={cn(toggleVariants({ variant, size, className }))}\r\n    {...props}\r\n  />\r\n));\r\n\r\nToggle.displayName = TogglePrimitive.Root.displayName;\r\n\r\nexport { Toggle, toggleVariants };\r\n", "path": "/components/ui/toggle.tsx", "target": "/components/ui/toggle.tsx"}]}