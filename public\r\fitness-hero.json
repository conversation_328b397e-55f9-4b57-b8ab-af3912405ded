{"name": "fitness-hero", "type": "registry:block", "dependencies": [], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "import { Button } from \"@/components/ui/button\";\r\n\r\nexport default function FitnessHero() {\r\n  return (\r\n    <div className=\"relative z-10\">\r\n      <div className=\"pb-20 pt-28 md:pt-16 lg:pt-20\">\r\n        <div className=\"container\">\r\n          <div className=\"grid items-center gap-10 lg:grid-cols-2 lg:gap-20 xl:gap-[122px]\">\r\n            <div className=\"mx-auto max-w-[500px] lg:mx-0 lg:max-w-none\">\r\n              <div className=\"flex flex-col items-center justify-center text-center lg:items-start lg:justify-normal lg:text-left\">\r\n                <h1 className=\"xxl:text-[90px] mb-6 text-4xl font-extrabold leading-[1.11] -tracking-[1px] md:text-5xl lg:text-6xl xl:text-7xl\">\r\n                  Fitness app for your good health\r\n                </h1>\r\n                <p className=\"mb-10 xl:mb-[50px]\">\r\n                  S<PERSON>ga is a health &amp; fitness tracker app that helps you set\r\n                  out realistic goals that you can accomplish without many\r\n                  hurdles. Sometimes, we keep bigger goals but end up and\r\n                  workout sessions and exercises to help you keep fit.\r\n                </p>\r\n                <div className=\"mb-8 flex flex-wrap items-center gap-[10px] lg:mb-[50px]\">\r\n                  <div className=\"flex\">\r\n                    <img\r\n                      src=\"https://blocks.mvp-subha.me/assets/fitness-hero/1.webp\"\r\n                      alt=\"hero-avatar-img-1\"\r\n                      width=\"58\"\r\n                      height=\"58\"\r\n                      className=\"-ml-[15px] h-[58px] w-[58px] rounded-[50%] first:-ml-0\"\r\n                    />\r\n                    <img\r\n                      src=\"https://blocks.mvp-subha.me/assets/fitness-hero/2.webp\"\r\n                      alt=\"hero-avatar-img-2\"\r\n                      width=\"58\"\r\n                      height=\"58\"\r\n                      className=\"-ml-[15px] h-[58px] w-[58px] rounded-[50%] first:-ml-0\"\r\n                    />\r\n                    <img\r\n                      src=\"https://blocks.mvp-subha.me/assets/fitness-hero/3.webp\"\r\n                      alt=\"hero-avatar-img-2\"\r\n                      width=\"58\"\r\n                      height=\"58\"\r\n                      className=\"-ml-[15px] h-[58px] w-[58px] rounded-[50%] first:-ml-0\"\r\n                    />\r\n                  </div>\r\n                  <ul className=\"flex gap-12\">\r\n                    <li className=\"relative after:absolute after:left-[calc(100%+_24px)] after:top-1/2 after:h-10 after:w-0.5 after:-translate-y-1/2 after:bg-black/20 last:after:hidden dark:after:bg-white/20\">\r\n                      <div className=\"text-[30px] font-bold leading-[1.4] -tracking-[1px]\">\r\n                        64,739\r\n                      </div>\r\n                      <div className=\"flex gap-[5px] text-base\">\r\n                        Happy Customers\r\n                      </div>\r\n                    </li>\r\n                    <li className=\"after:bg-ColorBlack/10 relative after:absolute after:left-[calc(100%+_24px)] after:top-1/2 after:h-10 after:w-0.5 after:-translate-y-1/2 last:after:hidden\">\r\n                      <div className=\"text-[30px] font-bold leading-[1.4] -tracking-[1px]\">\r\n                        4.8/5\r\n                      </div>\r\n                      <div className=\"flex gap-[5px] text-base\">\r\n                        <div className=\"flex gap-1\">\r\n                          <img\r\n                            src=\"https://blocks.mvp-subha.me/assets/fitness-hero/icon.svg\"\r\n                            alt=\"icon-green-star\"\r\n                            width=\"17\"\r\n                            height=\"17\"\r\n                          />\r\n                          <img\r\n                            src=\"https://blocks.mvp-subha.me/assets/fitness-hero/icon.svg\"\r\n                            alt=\"icon-green-star\"\r\n                            width=\"17\"\r\n                            height=\"17\"\r\n                          />\r\n                          <img\r\n                            src=\"https://blocks.mvp-subha.me/assets/fitness-hero/icon.svg\"\r\n                            alt=\"icon-green-star\"\r\n                            width=\"17\"\r\n                            height=\"17\"\r\n                          />\r\n                          <img\r\n                            src=\"https://blocks.mvp-subha.me/assets/fitness-hero/icon.svg\"\r\n                            alt=\"icon-green-star\"\r\n                            width=\"17\"\r\n                            height=\"17\"\r\n                          />\r\n                          <img\r\n                            src=\"https://blocks.mvp-subha.me/assets/fitness-hero/icon.svg\"\r\n                            alt=\"icon-green-star\"\r\n                            width=\"17\"\r\n                            height=\"17\"\r\n                          />\r\n                        </div>\r\n                        Rating\r\n                      </div>\r\n                    </li>\r\n                  </ul>\r\n                </div>\r\n\r\n                <div className=\"flex flex-wrap justify-center gap-5 sm:justify-normal\">\r\n                  <a href=\"#\" className=\"group relative z-10 inline-block\">\r\n                    <Button\r\n                      className=\"rounded-full shadow-md shadow-foreground/30\"\r\n                      size=\"lg\"\r\n                    >\r\n                      Start a 10-day free trial\r\n                    </Button>\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div>\r\n              <div className=\"relative z-10 mx-auto h-auto max-w-[280px] sm:max-w-[500px] lg:ml-auto lg:mr-0\">\r\n                <img\r\n                  src=\"https://blocks.mvp-subha.me/assets/fitness-hero/image.webp\"\r\n                  alt=\"hero-mobille\"\r\n                  width=\"612\"\r\n                  height=\"1272\"\r\n                  className=\"z-10 mx-auto h-[635px] w-[306px]\"\r\n                />\r\n                <img\r\n                  src=\"https://blocks.mvp-subha.me/assets/fitness-hero/shape1.svg\"\r\n                  alt=\"hero-2-shape-1\"\r\n                  width=\"110\"\r\n                  height=\"191\"\r\n                  className=\"xxl:left-[6%] absolute -left-[12%] bottom-[18%] -z-10 xl:-left-[2%]\"\r\n                />\r\n                <img\r\n                  src=\"https://blocks.mvp-subha.me/assets/fitness-hero/shape2.svg\"\r\n                  alt=\"hero-2-shape-1\"\r\n                  width=\"292\"\r\n                  height=\"299\"\r\n                  className=\"absolute -right-[18%] top-[15%] -z-10\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/hero/fitness-hero.tsx", "target": "/components/mvpblocks/fitness-hero.tsx"}]}