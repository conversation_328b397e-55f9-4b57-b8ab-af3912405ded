---
title: Scroll Animation
description: Motion Number is a component that displays a number with a motion effect.
---

import { But<PERSON> } from "@/components/ui/button";
import { Github } from "lucide-react";
import Link from "next/link";

<div className="flex flex-col items-center justify-start space-y-8 py-4 text-center">
  <div className="space-y-2">
    <h2 className="text-3xl mt-0 font-bold tracking-tighter sm:text-4xl md:text-5xl">Component In Development</h2>
    <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
      We're currently working on creating beautiful, responsive pagination components for MVPBlocks.
    </p>
  </div>

<div className="w-full max-w-md rounded-lg border border-border bg-card/40 p-6 shadow-sm">
  <div className="space-y-2">
    <h3 className="text-xl font-bold">Want to contribute?</h3>
    <p className="text-sm text-muted-foreground">
      We welcome contributions from the community! If you have ideas or
      implementations for pagination components, consider contributing to
      MVPBlocks.
    </p>
  </div>
</div>

  <Link href="https://github.com/subhadeeproy3902/mvpblocks" target="_blank" rel="noopener noreferrer">
    <Button className="gap-2">
      <Github className="h-4 w-4" />
      Contribute on GitHub
    </Button>
  </Link>
</div>
