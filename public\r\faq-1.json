{"name": "faq-1", "type": "registry:block", "dependencies": ["@radix-ui/react-accordion", "framer-motion", "lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/accordion.json"], "files": [{"type": "registry:block", "content": "import * as AccordionPrimitive from \"@radix-ui/react-accordion\";\r\nimport { PlusIcon } from \"lucide-react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n} from \"@/components/ui/accordion\";\r\n\r\nconst items = [\r\n  {\r\n    id: \"1\",\r\n    title: \"What makes MVPBlocks different?\",\r\n    content:\r\n      \"MVPBlocks is a fully open-source, developer-first component library built using Next.js and TailwindCSS, designed to help you launch your MVPs in record time. No bloated packages, no unnecessary installs—just clean, copyable code to plug right into your next big thing.\",\r\n  },\r\n  {\r\n    id: \"2\",\r\n    title: \"How can I customize the components?\",\r\n    content:\r\n      \"All components are built with Tailwind CSS, making them highly customizable. Simply modify the class names or use our theme variables to match your brand. Components also support both light and dark modes out of the box.\",\r\n  },\r\n  {\r\n    id: \"3\",\r\n    title: \"Are MVPBlocks components responsive?\",\r\n    content:\r\n      \"Absolutely! All components are designed to be fully responsive and work beautifully on all devices, from mobile phones to large desktop screens. We've carefully crafted each component to provide an optimal experience regardless of screen size.\",\r\n  },\r\n  {\r\n    id: \"4\",\r\n    title: \"Can I use MVPBlocks for commercial projects?\",\r\n    content:\r\n      \"Yes, all MVPBlocks components are free to use for both personal and commercial projects. No attribution required—just build and launch your MVP faster than ever before.\",\r\n  },\r\n  {\r\n    id: \"5\",\r\n    title: \"How do I get started with MVPBlocks?\",\r\n    content:\r\n      \"Simply browse our component library, find the components you need, and copy the code into your project. It's that easy! Our documentation provides clear instructions for installation and usage.\",\r\n  },\r\n];\r\n\r\nconst fadeInAnimationVariants = {\r\n  initial: {\r\n    opacity: 0,\r\n    y: 10,\r\n  },\r\n  animate: (index: number) => ({\r\n    opacity: 1,\r\n    y: 0,\r\n    transition: {\r\n      delay: 0.05 * index,\r\n      duration: 0.4,\r\n    },\r\n  }),\r\n};\r\n\r\nexport default function Faq1() {\r\n  return (\r\n    <section className=\"py-12 md:py-16\">\r\n      <div className=\"container mx-auto max-w-6xl px-4 md:px-6\">\r\n        <div className=\"mb-10 text-center\">\r\n          <motion.h2\r\n            className=\"mb-4 text-3xl font-bold tracking-tight md:text-4xl\"\r\n            initial={{ opacity: 0, y: -10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5 }}\r\n          >\r\n            Frequently Asked <span className=\"bg-gradient-to-r from-primary to-rose-400 bg-clip-text text-transparent\">Questions</span>\r\n          </motion.h2>\r\n          <motion.p\r\n            className=\"mx-auto max-w-2xl text-muted-foreground\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.5, delay: 0.2 }}\r\n          >\r\n            Everything you need to know about MVPBlocks and how to use our components to build your next project quickly.\r\n          </motion.p>\r\n        </div>\r\n\r\n        <motion.div\r\n          className=\"relative mx-auto max-w-3xl\"\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ duration: 0.5, delay: 0.3 }}\r\n        >\r\n          {/* Decorative gradient */}\r\n          <div className=\"absolute -left-4 -top-4 -z-10 h-72 w-72 rounded-full bg-primary/10 blur-3xl\" />\r\n          <div className=\"absolute -bottom-4 -right-4 -z-10 h-72 w-72 rounded-full bg-primary/10 blur-3xl\" />\r\n\r\n          <Accordion\r\n            type=\"single\"\r\n            collapsible\r\n            className=\"w-full rounded-xl border border-border/40 bg-card/30 p-2 backdrop-blur-sm\"\r\n            defaultValue=\"1\"\r\n          >\r\n            {items.map((item, index) => (\r\n              <motion.div\r\n                key={item.id}\r\n                custom={index}\r\n                variants={fadeInAnimationVariants}\r\n                initial=\"initial\"\r\n                whileInView=\"animate\"\r\n                viewport={{ once: true }}\r\n              >\r\n                <AccordionItem\r\n                  value={item.id}\r\n                  className={cn(\r\n                    \"my-1 overflow-hidden rounded-lg border-none bg-card/50 px-2 shadow-sm transition-all\",\r\n                    \"data-[state=open]:bg-card/80 data-[state=open]:shadow-md\"\r\n                  )}\r\n                >\r\n                  <AccordionPrimitive.Header className=\"flex\">\r\n                    <AccordionPrimitive.Trigger\r\n                      className={cn(\r\n                        \"group flex flex-1 items-center justify-between gap-4 py-4 text-left text-base font-medium\",\r\n                        \"outline-none transition-all duration-300 hover:text-primary\",\r\n                        \"focus-visible:ring-2 focus-visible:ring-primary/50\",\r\n                        \"data-[state=open]:text-primary\"\r\n                      )}\r\n                    >\r\n                      {item.title}\r\n                      <PlusIcon\r\n                        size={18}\r\n                        className={cn(\r\n                          \"text-primary/70 shrink-0 transition-transform duration-300 ease-out\",\r\n                          \"group-data-[state=open]:rotate-45\"\r\n                        )}\r\n                        aria-hidden=\"true\"\r\n                      />\r\n                    </AccordionPrimitive.Trigger>\r\n                  </AccordionPrimitive.Header>\r\n                  <AccordionContent\r\n                    className={cn(\r\n                      \"overflow-hidden pb-4 pt-0 text-muted-foreground\",\r\n                      \"data-[state=open]:animate-accordion-down\",\r\n                      \"data-[state=closed]:animate-accordion-up\"\r\n                    )}\r\n                  >\r\n                    <div className=\"border-t border-border/30 pt-3\">\r\n                      {item.content}\r\n                    </div>\r\n                  </AccordionContent>\r\n                </AccordionItem>\r\n              </motion.div>\r\n            ))}\r\n          </Accordion>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/faqs/faq-1.tsx", "target": "/components/mvpblocks/faq-1.tsx"}]}