{"name": "about-us-2", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "next-themes", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/border-beam.json", "https://blocks.mvp-subha.me/r/counter.json", "https://blocks.mvp-subha.me/r/spotlight.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport { motion, useInView } from \"framer-motion\";\r\nimport { useRef } from \"react\";\r\nimport { NumberTicker } from \"@/components/ui/counter\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport {\r\n  Users,\r\n  Award,\r\n  Briefcase,\r\n  Code,\r\n  Sparkles,\r\n  Building,\r\n  LineChart,\r\n  CheckCircle,\r\n  Clock,\r\n  Zap,\r\n} from \"lucide-react\";\r\n\r\ninterface StatItemProps {\r\n  value: number;\r\n  label: string;\r\n  icon: React.ReactNode;\r\n  delay?: number;\r\n  decimalPlaces?: number;\r\n  color?: string;\r\n}\r\n\r\nconst StatItem = ({ value, label, icon, delay = 0, decimalPlaces = 0, color = \"from-primary to-primary/70\" }: StatItemProps) => {\r\n  const ref = useRef(null);\r\n  const isInView = useInView(ref, { once: true, amount: 0.3 });\r\n  const { resolvedTheme } = useTheme();\r\n\r\n  return (\r\n    <motion.div\r\n      ref={ref}\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\r\n      transition={{ duration: 0.6, delay: delay, ease: \"easeOut\" }}\r\n      className={cn(\r\n        \"group relative overflow-hidden rounded-xl border border-border/30 bg-card p-6\",\r\n        resolvedTheme === \"dark\" ? \"shadow-xl shadow-black/5\" : \"shadow-lg shadow-black/[0.03]\"\r\n      )}\r\n    >\r\n      <div\r\n        className={cn(\r\n          \"absolute -right-6 -top-6 h-24 w-24 rounded-full bg-gradient-to-br opacity-20 blur-2xl transition-all duration-500 group-hover:opacity-30 group-hover:blur-3xl\",\r\n          color\r\n        )}\r\n      />\r\n\r\n      <div className=\"flex items-center gap-4\">\r\n        <div className={cn(\r\n          \"flex h-12 w-12 shrink-0 items-center justify-center rounded-lg bg-gradient-to-br text-white\",\r\n          color\r\n        )}>\r\n          {icon}\r\n        </div>\r\n\r\n        <div className=\"flex flex-col\">\r\n          <h3 className=\"flex items-baseline text-3xl font-bold tracking-tight\">\r\n            <NumberTicker\r\n              value={value}\r\n              decimalPlaces={decimalPlaces}\r\n              className=\"tabular-nums\"\r\n            />\r\n            <span className=\"ml-1 text-sm font-medium opacity-70\">+</span>\r\n          </h3>\r\n          <p className=\"text-sm font-medium text-muted-foreground\">{label}</p>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport default function AboutUs2() {\r\n  const aboutRef = useRef(null);\r\n  const statsRef = useRef(null);\r\n  const timelineRef = useRef(null);\r\n\r\n  const aboutInView = useInView(aboutRef, { once: true, amount: 0.3 });\r\n  const timelineInView = useInView(timelineRef, { once: true, amount: 0.2 });\r\n\r\n  const stats = [\r\n    {\r\n      value: 5000,\r\n      label: \"Happy Clients\",\r\n      icon: <Users className=\"h-5 w-5\" />,\r\n      delay: 0,\r\n      color: \"from-rose-500 to-orange-500\",\r\n      decimalPlaces: 0,\r\n    },\r\n    {\r\n      value: 15,\r\n      label: \"Years Experience\",\r\n      icon: <Clock className=\"h-5 w-5\" />,\r\n      delay: 0.1,\r\n      color: \"from-blue-500 to-cyan-500\",\r\n      decimalPlaces: 0,\r\n    },\r\n    {\r\n      value: 100,\r\n      label: \"Projects Completed\",\r\n      icon: <CheckCircle className=\"h-5 w-5\" />,\r\n      delay: 0.2,\r\n      color: \"from-green-500 to-emerald-500\",\r\n      decimalPlaces: 0,\r\n    },\r\n    {\r\n      value: 24,\r\n      label: \"Awards Won\",\r\n      icon: <Award className=\"h-5 w-5\" />,\r\n      delay: 0.3,\r\n      color: \"from-purple-500 to-violet-500\",\r\n      decimalPlaces: 0,\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <section className=\"relative overflow-hidden py-16 w-full md:py-24\">\r\n      {/* Background pattern */}\r\n      <div className=\"absolute inset-0 -z-10 opacity-[0.02] dark:opacity-[0.05]\">\r\n        <svg className=\"h-full w-full\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <defs>\r\n            <pattern id=\"grid\" width=\"40\" height=\"40\" patternUnits=\"userSpaceOnUse\">\r\n              <path d=\"M 40 0 L 0 0 0 40\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\" />\r\n            </pattern>\r\n          </defs>\r\n          <rect width=\"100%\" height=\"100%\" fill=\"url(#grid)\" />\r\n        </svg>\r\n      </div>\r\n\r\n      <div className=\"container relative z-10 mx-auto max-w-6xl px-4 md:px-6\">\r\n        {/* Header Section with Badge */}\r\n        <div className=\"mx-auto mb-16 max-w-3xl text-center\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5, ease: \"easeOut\" }}\r\n            className=\"mb-4 flex justify-center\"\r\n          >\r\n            <Badge\r\n              variant=\"outline\"\r\n              className=\"rounded-full border-primary/20 bg-primary/5 px-4 py-1 text-sm font-medium\"\r\n            >\r\n              <Sparkles className=\"mr-1 h-3.5 w-3.5 text-primary\" />\r\n              About Us\r\n            </Badge>\r\n          </motion.div>\r\n\r\n          <motion.h1\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.1, ease: \"easeOut\" }}\r\n            className=\"bg-gradient-to-b from-foreground to-foreground/70 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl\"\r\n          >\r\n            About Our Company\r\n          </motion.h1>\r\n\r\n          <motion.p\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.2, ease: \"easeOut\" }}\r\n            className=\"mt-4 text-xl text-muted-foreground\"\r\n          >\r\n            Delivering excellence for over 15 years\r\n          </motion.p>\r\n        </div>\r\n\r\n        {/* Stats Section */}\r\n        <div\r\n          ref={statsRef}\r\n          className=\"mb-20\"\r\n        >\r\n          <div className=\"grid gap-5 sm:grid-cols-2 lg:grid-cols-4\">\r\n            {stats.map((stat, index) => (\r\n              <StatItem\r\n                key={index}\r\n                value={stat.value}\r\n                label={stat.label}\r\n                icon={stat.icon}\r\n                delay={stat.delay || index * 0.1}\r\n                decimalPlaces={stat.decimalPlaces}\r\n                color={stat.color}\r\n              />\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* About Content Section */}\r\n        <div\r\n          ref={aboutRef}\r\n          className=\"relative mx-auto mb-20\"\r\n        >\r\n          <div className=\"grid gap-16 md:grid-cols-2\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 30 }}\r\n              animate={aboutInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\r\n              transition={{ duration: 0.7, delay: 0.1, ease: \"easeOut\" }}\r\n              className=\"relative space-y-6\"\r\n            >\r\n              <div className=\"inline-flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-primary/80 to-primary/60 text-white shadow-lg\">\r\n                <Zap className=\"h-6 w-6\" />\r\n              </div>\r\n\r\n              <h2 className=\"text-2xl font-bold tracking-tight\">Our Mission</h2>\r\n\r\n              <p className=\"text-base leading-relaxed text-muted-foreground\">\r\n                To empower businesses with innovative digital solutions that drive growth,\r\n                enhance user experiences, and create lasting value in an ever-evolving\r\n                technological landscape.\r\n              </p>\r\n            </motion.div>\r\n\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 30 }}\r\n              animate={aboutInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\r\n              transition={{ duration: 0.7, delay: 0.3, ease: \"easeOut\" }}\r\n              className=\"relative space-y-6\"\r\n            >\r\n              <div className=\"inline-flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500/80 to-blue-500/60 text-white shadow-lg\">\r\n                <LineChart className=\"h-6 w-6\" />\r\n              </div>\r\n\r\n              <h2 className=\"text-2xl font-bold tracking-tight\">Our Vision</h2>\r\n\r\n              <p className=\"text-base leading-relaxed text-muted-foreground\">\r\n                To be the leading provider of transformative digital experiences,\r\n                recognized globally for our commitment to excellence, innovation,\r\n                and client success.\r\n              </p>\r\n            </motion.div>\r\n          </div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 30 }}\r\n            animate={aboutInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\r\n            transition={{ duration: 0.7, delay: 0.5, ease: \"easeOut\" }}\r\n            className=\"mt-16 flex items-start gap-4\"\r\n          >\r\n            <div className=\"inline-flex h-10 w-10 shrink-0 items-center justify-center rounded-lg bg-gradient-to-br from-primary/20 to-primary/5 text-primary\">\r\n              <Building className=\"h-5 w-5\" />\r\n            </div>\r\n            <p className=\"text-base leading-relaxed text-muted-foreground\">\r\n              We are a passionate team of experts dedicated to delivering exceptional solutions that help businesses thrive in the digital landscape. Our commitment to innovation and quality has made us a trusted partner for organizations worldwide.\r\n            </p>\r\n          </motion.div>\r\n        </div>\r\n\r\n        {/* Timeline Section */}\r\n        <div ref={timelineRef} className=\"relative mx-auto max-w-4xl\">\r\n          <motion.h2\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={timelineInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\r\n            transition={{ duration: 0.6, ease: \"easeOut\" }}\r\n            className=\"mb-10 text-center text-2xl font-bold tracking-tight md:text-3xl\"\r\n          >\r\n            Our Journey\r\n          </motion.h2>\r\n\r\n          <div className=\"relative ml-4 border-l border-border/60 pl-8 md:ml-0 md:border-none md:pl-0\">\r\n            {[\r\n              { year: \"2008\", title: \"Founded\", description: \"Our company was established with a vision to transform digital experiences.\" },\r\n              { year: \"2015\", title: \"Global Expansion\", description: \"Expanded operations to serve clients across 20+ countries worldwide.\" },\r\n              { year: \"2019\", title: \"Innovation Award\", description: \"Recognized for our cutting-edge solutions and technological innovation.\" },\r\n              { year: \"2023\", title: \"New Horizons\", description: \"Launched new service offerings to meet evolving market demands.\" }\r\n            ].map((item, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, x: -20 }}\r\n                animate={timelineInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}\r\n                transition={{ duration: 0.5, delay: 0.1 * index, ease: \"easeOut\" }}\r\n                className=\"relative mb-10 md:grid md:grid-cols-5 md:gap-8\"\r\n              >\r\n                <div className=\"md:col-span-1\">\r\n                  <div className=\"absolute -left-12 flex h-8 w-8 items-center justify-center rounded-full border border-border bg-card text-sm font-bold md:static md:h-auto md:w-auto md:rounded-none md:border-none md:bg-transparent md:text-xl\">\r\n                    {item.year}\r\n                  </div>\r\n                </div>\r\n                <div className=\"md:col-span-4\">\r\n                  <h3 className=\"text-lg font-bold md:text-xl\">{item.title}</h3>\r\n                  <p className=\"mt-1 text-muted-foreground\">{item.description}</p>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/about/about-us-2.tsx", "target": "/components/mvpblocks/about-us-2.tsx"}]}