{"name": "bolt", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "import {\r\n  Paperclip,\r\n  Terminal,\r\n  Figma,\r\n  FileUp,\r\n  MonitorIcon,\r\n  <PERSON>rkles,\r\n} from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nconst EXAMPLE_ACTIONS = [\r\n  { icon: <Figma className=\"h-4 w-4\" />, text: \"Import from Figma\" },\r\n  {\r\n    icon: <FileUp className=\"h-4 w-4\" />,\r\n    text: \"Build a mobile app with Expo\",\r\n  },\r\n  {\r\n    icon: <MonitorIcon className=\"h-4 w-4\" />,\r\n    text: \"Start a blog with Astro\",\r\n  },\r\n  {\r\n    icon: <Terminal className=\"h-4 w-4\" />,\r\n    text: \"Create a docs site with Vitepress\",\r\n  },\r\n  { icon: <FileUp className=\"h-4 w-4\" />, text: \"Scaffold UI with shadcn\" },\r\n];\r\n\r\nexport default function BoltChat() {\r\n  return (\r\n    <div className=\"selection-accent flex flex-grow flex-col py-20\">\r\n      <div className=\"mx-4 flex flex-col\">\r\n        <div className=\"mb-8 text-center\">\r\n          <h1 className=\"mb-4 text-5xl font-bold\">\r\n            What do you want to build?\r\n          </h1>\r\n          <p className=\"text-lg text-gray-400\">\r\n            Prompt, run, edit, and deploy full-stack{\" \"}\r\n            <span className=\"font-medium text-foreground\">web</span> and{\" \"}\r\n            <span className=\"font-medium text-foreground\">mobile</span> apps.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"mx-auto mb-6 w-full max-w-lg\">\r\n          <div className=\"relative h-[26px]\">\r\n            <div className=\"duration-350 absolute left-2 top-0 flex w-[calc(100%-1rem)] flex-wrap justify-between truncate rounded-t-lg border bg-secondary/50 px-2 py-1 text-xs opacity-100 backdrop-blur transition-opacity\">\r\n              <span>150K daily tokens remaining.</span>\r\n              <button className=\"mr-4 inline-block bg-transparent font-semibold text-primary hover:underline\">\r\n                Subscribe to Pro for 66x more usage\r\n              </button>\r\n            </div>\r\n          </div>\r\n          <div className=\"shadow-xs relative rounded-lg backdrop-blur\">\r\n            <div className=\"flex flex-col rounded-lg border bg-secondary/50 p-4\">\r\n              <textarea\r\n                placeholder=\"How can Bolt help you today?\"\r\n                className=\"mb-4 h-24 w-full resize-none bg-transparent outline-none\"\r\n              />\r\n\r\n              <div className=\"mt-auto flex gap-4\">\r\n                <button className=\"text-zinc-400\">\r\n                  <Paperclip size={20} />\r\n                </button>\r\n                <button className=\"text-zinc-400\">\r\n                  <Sparkles size={20} />\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"mx-auto mt-8 flex w-full max-w-3xl flex-wrap justify-center gap-2\">\r\n          {EXAMPLE_ACTIONS.map((action, index) => (\r\n            <Button\r\n              key={index}\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n              className=\"rounded-full px-4 py-0.5 text-xs\"\r\n            >\r\n              {action.icon}\r\n              <span>{action.text}</span>\r\n            </Button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/chatbot-ui/bolt.tsx", "target": "/components/mvpblocks/bolt.tsx"}]}