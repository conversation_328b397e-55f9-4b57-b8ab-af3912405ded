{"name": "retro-card", "type": "registry:block", "dependencies": [], "registryDependencies": [], "files": [{"type": "registry:block", "content": "export default function RetroCard() {\n  return (\n    <article className=\"flex w-full max-w-sm flex-col items-start justify-between border-4 border-black bg-background p-6 shadow-[8px_8px_0_0_#000] transition-shadow duration-300 hover:shadow-[12px_12px_0_0_#000] dark:border-white dark:shadow-[8px_8px_0_0_#fff] dark:hover:shadow-[12px_12px_0_0_#fff]\">\n      <div className=\"mb-2 flex items-center gap-x-2 text-xs\">\n        <div className=\"border-2 border-black bg-red-500 px-3 py-1 font-bold text-foreground dark:border-white\">\n          May 12, 2024\n        </div>\n        <a\n          href=\"#\"\n          className=\"relative z-10 border-2 border-border bg-red-500 px-3 py-1 font-bold text-foreground transition-colors duration-300 hover:bg-blue-700\"\n        >\n          Design System\n        </a>\n      </div>\n      <div className=\"group relative\">\n        <h3 className=\"group-hover:text-red-5-0 mt-3 text-2xl font-black uppercase leading-6 text-foreground\">\n          <a href=\"#\">\n            <span className=\"absolute inset-0 max-w-xs\"></span>Master Atomic\n            Design in React\n          </a>\n        </h3>\n        <p className=\"text-md mt-5 border-l-4 border-red-500 pl-4 leading-6 text-gray-800 dark:text-gray-100\">\n          Learn how to implement atomic design principles in your React\n          projects. Boost your component reusability and maintain a consistent\n          design system.\n        </p>\n      </div>\n      <div className=\"relative mt-8 flex items-center gap-x-2\">\n        <div className=\"text-sm leading-6\">\n          <p className=\"font-black text-foreground\">\n            <a href=\"#\" className=\"hover:underline\">\n              <span className=\"absolute inset-0\"></span>Sarah Parker\n            </a>\n          </p>\n          <p className=\"font-bold text-gray-700 dark:text-gray-200\">\n            Senior UI Engineer\n          </p>\n        </div>\n      </div>\n    </article>\n  );\n}\n", "path": "/components/mvpblocks/cards/basic/retro-card.tsx", "target": "/components/mvpblocks/retro-card.tsx"}]}