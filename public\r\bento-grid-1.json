{"name": "bento-grid-1", "type": "registry:block", "author": "Xeven777", "dependencies": ["lucide-react", "framer-motion"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "\"use client\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { motion } from \"framer-motion\";\r\nimport { ArrowRight, Code, FileText, Layers, Palette, Zap } from \"lucide-react\";\r\n\r\ninterface BentoGridItemProps {\r\n  title: string;\r\n  description: string;\r\n  icon: React.ReactNode;\r\n  className?: string;\r\n  size?: \"small\" | \"medium\" | \"large\";\r\n}\r\n\r\nconst BentoGridItem = ({\r\n  title,\r\n  description,\r\n  icon,\r\n  className,\r\n  size = \"small\",\r\n}: BentoGridItemProps) => {\r\n  const variants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: { opacity: 1, y: 0, transition: { type: \"spring\", damping: 25 } },\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      variants={variants}\r\n      className={cn(\r\n        \"group relative flex h-full cursor-pointer flex-col justify-between overflow-hidden rounded-xl border border-primary/10 bg-background px-6 pb-10 pt-6 shadow-md transition-all duration-500 hover:border-primary/30\",\r\n        className,\r\n      )}\r\n    >\r\n      <div className=\"absolute -right-1/2 top-0 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] bg-[size:24px_24px] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)]\"></div>\r\n\r\n      <div className=\"absolute bottom-3 right-1 scale-[6] text-primary/5 transition-all duration-700 group-hover:scale-[6.2] group-hover:text-primary/10\">\r\n        {icon}\r\n      </div>\r\n\r\n      <div className=\"relative z-10 flex h-full flex-col justify-between\">\r\n        <div>\r\n          <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary shadow shadow-primary/10 transition-all duration-500 group-hover:bg-primary/20 group-hover:shadow-primary/20\">\r\n            {icon}\r\n          </div>\r\n          <h3 className=\"mb-2 text-xl font-semibold tracking-tight\">{title}</h3>\r\n          <p className=\"text-sm text-muted-foreground\">{description}</p>\r\n        </div>\r\n        <div className=\"mt-4 flex items-center text-sm text-primary\">\r\n          <span className=\"mr-1\">Learn more</span>\r\n          <ArrowRight className=\"size-4 transition-all duration-500 group-hover:translate-x-2\" />\r\n        </div>\r\n      </div>\r\n      <div className=\"absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r from-primary to-primary/30 blur-2xl transition-all duration-500 group-hover:blur-lg\" />\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nconst items = [\r\n  {\r\n    title: \"Developer Experience\",\r\n    description:\r\n      \"Built with developers in mind, making implementation a breeze.\",\r\n    icon: <Code className=\"size-6\" />,\r\n    size: \"large\" as const,\r\n  },\r\n  {\r\n    title: \"Accessibility\",\r\n    description:\r\n      \"Built with a11y best practices to ensure your app is usable by everyone.\",\r\n    icon: <Layers className=\"size-6\" />,\r\n    size: \"small\" as const,\r\n  },\r\n  {\r\n    title: \"Responsive Design\",\r\n    description: \"Create layouts that adapt to any screen size with ease.\",\r\n    icon: <Layers className=\"size-6\" />,\r\n    size: \"medium\" as const,\r\n  },\r\n  {\r\n    title: \"Customizable\",\r\n    description: \"Tailor components to match your brand's unique style.\",\r\n    icon: <Palette className=\"size-6\" />,\r\n    size: \"medium\" as const,\r\n  },\r\n  {\r\n    title: \"Performance\",\r\n    description: \"Optimized for speed and efficiency across all devices.\",\r\n    icon: <Zap className=\"size-6\" />,\r\n    size: \"small\" as const,\r\n  },\r\n  {\r\n    title: \"Documentation\",\r\n    description:\r\n      \"Comprehensive guides and examples to help you get started quickly.\",\r\n    icon: <FileText className=\"size-6\" />,\r\n    size: \"large\" as const,\r\n  },\r\n];\r\n\r\nexport default function BentoGrid1() {\r\n  const containerVariants = {\r\n    hidden: {},\r\n    visible: {\r\n      transition: {\r\n        staggerChildren: 0.12,\r\n        delayChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <div className=\"mx-auto max-w-6xl px-4 py-12\">\r\n      <motion.div\r\n        className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-6\"\r\n        variants={containerVariants}\r\n        initial=\"hidden\"\r\n        animate=\"visible\"\r\n      >\r\n        {items.map((item, i) => (\r\n          <BentoGridItem\r\n            key={i}\r\n            title={item.title}\r\n            description={item.description}\r\n            icon={item.icon}\r\n            size={item.size}\r\n            className={cn(\r\n              item.size === \"large\"\r\n                ? \"col-span-4\"\r\n                : item.size === \"medium\"\r\n                  ? \"col-span-3\"\r\n                  : \"col-span-2\",\r\n              \"h-full\",\r\n            )}\r\n          />\r\n        ))}\r\n      </motion.div>\r\n    </div>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/grids/bento-grid-1.tsx", "target": "/components/mvpblocks/bento-grid-1.tsx"}]}