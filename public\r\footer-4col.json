{"name": "footer-4col", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "import {\r\n  Dribbble,\r\n  Facebook,\r\n  Github,\r\n  Instagram,\r\n  Mail,\r\n  MapPin,\r\n  Phone,\r\n  Twitter,\r\n} from \"lucide-react\";\r\nimport Link from \"next/link\";\r\n\r\nconst socialLinks = [\r\n  { icon: Facebook, label: \"Facebook\" },\r\n  { icon: Instagram, label: \"Instagram\" },\r\n  { icon: Twitter, label: \"Twitter\" },\r\n  { icon: Github, label: \"GitHub\" },\r\n  { icon: Dribbble, label: \"Dribbble\" },\r\n];\r\n\r\nconst aboutLinks = [\r\n  { text: \"Company History\", href: \"#\" },\r\n  { text: \"Meet the Team\", href: \"#\" },\r\n  { text: \"Employee Handbook\", href: \"#\" },\r\n  { text: \"Careers\", href: \"#\" },\r\n];\r\n\r\nconst serviceLinks = [\r\n  { text: \"Web Development\", href: \"#\" },\r\n  { text: \"Web Design\", href: \"#\" },\r\n  { text: \"Marketing\", href: \"#\" },\r\n  { text: \"Google Ads\", href: \"#\" },\r\n];\r\n\r\nconst helpfulLinks = [\r\n  { text: \"FAQs\", href: \"#\" },\r\n  { text: \"Support\", href: \"#\" },\r\n  { text: \"Live Chat\", href: \"#\", hasIndicator: true },\r\n];\r\n\r\nconst contactInfo = [\r\n  { icon: Mail, text: \"Subhadeep\" },\r\n  { icon: Phone, text: \"8637373116\" },\r\n  { icon: MapPin, text: \"Kolkata, India\", isAddress: true },\r\n];\r\n\r\nexport default function Footer4Col() {\r\n  return (\r\n    <footer className=\"mt-16 w-full place-self-end rounded-t-xl bg-secondary dark:bg-secondary/20\">\r\n      <div className=\"mx-auto max-w-screen-xl px-4 pb-6 pt-16 sm:px-6 lg:px-8 lg:pt-24\">\r\n        <div className=\"grid grid-cols-1 gap-8 lg:grid-cols-3\">\r\n          <div>\r\n            <div className=\"flex justify-center gap-2 text-primary sm:justify-start\">\r\n              <img\r\n                src=\"/logo.webp\"\r\n                alt=\"logo\"\r\n                className=\"h-8 w-8 rounded-full\"\r\n              />\r\n              <span className=\"bg-primary from-foreground via-rose-200 to-primary bg-clip-text text-2xl font-semibold text-transparent dark:bg-gradient-to-b\">\r\n                Mvpblocks\r\n              </span>\r\n            </div>\r\n\r\n            <p className=\"mt-6 max-w-md text-center leading-relaxed text-foreground/50 sm:max-w-xs sm:text-left\">\r\n              Lorem ipsum dolor, sit amet consectetur adipisicing elit. Incidunt\r\n              consequuntur amet culpa cum itaque neque.\r\n            </p>\r\n\r\n            <ul className=\"mt-8 flex justify-center gap-6 sm:justify-start md:gap-8\">\r\n              {socialLinks.map(({ icon: Icon, label }) => (\r\n                <li key={label}>\r\n                  <Link\r\n                    href=\"#\"\r\n                    className=\"text-primary transition hover:text-primary/80\"\r\n                  >\r\n                    <span className=\"sr-only\">{label}</span>\r\n                    <Icon className=\"size-6\" />\r\n                  </Link>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-4 lg:col-span-2\">\r\n            <div className=\"text-center sm:text-left\">\r\n              <p className=\"text-lg font-medium\">About Us</p>\r\n\r\n              <ul className=\"mt-8 space-y-4 text-sm\">\r\n                {aboutLinks.map(({ text, href }) => (\r\n                  <li key={text}>\r\n                    <a\r\n                      className=\"text-secondary-foreground/70 transition\"\r\n                      href={href}\r\n                    >\r\n                      {text}\r\n                    </a>\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            </div>\r\n\r\n            <div className=\"text-center sm:text-left\">\r\n              <p className=\"text-lg font-medium\">Our Services</p>\r\n\r\n              <ul className=\"mt-8 space-y-4 text-sm\">\r\n                {serviceLinks.map(({ text, href }) => (\r\n                  <li key={text}>\r\n                    <a\r\n                      className=\"text-secondary-foreground/70 transition\"\r\n                      href={href}\r\n                    >\r\n                      {text}\r\n                    </a>\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            </div>\r\n\r\n            <div className=\"text-center sm:text-left\">\r\n              <p className=\"text-lg font-medium\">Helpful Links</p>\r\n\r\n              <ul className=\"mt-8 space-y-4 text-sm\">\r\n                {helpfulLinks.map(({ text, href, hasIndicator }) => (\r\n                  <li key={text}>\r\n                    <a\r\n                      href={href}\r\n                      className={`${\r\n                        hasIndicator\r\n                          ? \"group flex justify-center gap-1.5 sm:justify-start\"\r\n                          : \"text-secondary-foreground/70 transition\"\r\n                      }`}\r\n                    >\r\n                      <span className=\"text-secondary-foreground/70 transition\">\r\n                        {text}\r\n                      </span>\r\n                      {hasIndicator && (\r\n                        <span className=\"relative flex size-2\">\r\n                          <span className=\"absolute inline-flex h-full w-full animate-ping rounded-full bg-primary opacity-75\" />\r\n                          <span className=\"relative inline-flex size-2 rounded-full bg-primary\" />\r\n                        </span>\r\n                      )}\r\n                    </a>\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            </div>\r\n\r\n            <div className=\"text-center sm:text-left\">\r\n              <p className=\"text-lg font-medium\">Contact Us</p>\r\n\r\n              <ul className=\"mt-8 space-y-4 text-sm\">\r\n                {contactInfo.map(({ icon: Icon, text, isAddress }) => (\r\n                  <li key={text}>\r\n                    <a\r\n                      className=\"flex items-center justify-center gap-1.5 sm:justify-start\"\r\n                      href=\"#\"\r\n                    >\r\n                      <Icon className=\"size-5 shrink-0 text-primary shadow-sm\" />\r\n                      {isAddress ? (\r\n                        <address className=\"-mt-0.5 flex-1 not-italic text-secondary-foreground/70 transition\">\r\n                          {text}\r\n                        </address>\r\n                      ) : (\r\n                        <span className=\"flex-1 text-secondary-foreground/70 transition\">\r\n                          {text}\r\n                        </span>\r\n                      )}\r\n                    </a>\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"mt-12 border-t pt-6\">\r\n          <div className=\"text-center sm:flex sm:justify-between sm:text-left\">\r\n            <p className=\"text-sm\">\r\n              <span className=\"block sm:inline\">All rights reserved.</span>\r\n            </p>\r\n\r\n            <p className=\"text-secondary-foreground/70-foreground mt-4 text-sm transition sm:order-first sm:mt-0\">\r\n              &copy; 2025 Mvpblocks\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/required/footers/footer-4col.tsx", "target": "/components/mvpblocks/footer-4col.tsx"}]}