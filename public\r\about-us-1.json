{"name": "about-us-1", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/border-beam.json", "https://blocks.mvp-subha.me/r/pulse-card.json", "https://blocks.mvp-subha.me/r/spotlight.json"], "files": [{"type": "registry:block", "content": "\"use client\";\n\nimport { motion, useInView } from \"framer-motion\";\nimport { useRef } from \"react\";\nimport { Spotlight } from \"@/components/ui/spotlight\";\nimport { BorderBeam } from \"@/components/ui/border-beam\";\nimport { CardHoverEffect } from \"@/components/ui/pulse-card\";\nimport {\n  Globe,\n  Users,\n  Heart,\n  Lightbulb,\n  Sparkles,\n  Rocket,\n  Target,\n} from \"lucide-react\";\n\ninterface AboutUsProps {\n  title?: string;\n  subtitle?: string;\n  mission?: string;\n  vision?: string;\n  values?: Array<{\n    title: string;\n    description: string;\n    icon: keyof typeof iconComponents;\n  }>;\n  className?: string;\n}\n\nconst iconComponents = {\n  Users: Users,\n  Heart: Heart,\n  Lightbulb: Lightbulb,\n  Globe: Globe,\n  Sparkles: Sparkles,\n  Rocket: Rocket,\n  Target: Target,\n};\n\nconst defaultValues: AboutUsProps[\"values\"] = [\n  {\n    title: \"Innovation\",\n    description:\n      \"We constantly push boundaries and explore new possibilities to create cutting-edge solutions.\",\n    icon: \"Lightbulb\",\n  },\n  {\n    title: \"Collaboration\",\n    description:\n      \"We believe in the power of teamwork and diverse perspectives to achieve extraordinary results.\",\n    icon: \"Users\",\n  },\n  {\n    title: \"Excellence\",\n    description:\n      \"We strive for perfection in everything we do, consistently delivering high-quality work.\",\n    icon: \"Sparkles\",\n  },\n  {\n    title: \"Impact\",\n    description:\n      \"We measure our success by the positive difference we make in people's lives and businesses.\",\n    icon: \"Globe\",\n  },\n];\n\nexport default function AboutUs1() {\n  const aboutData = {\n    title: \"About Us\",\n    subtitle: \"Building the future of web development with beautiful, reusable components.\",\n    mission: \"Our mission is to democratize web development by providing high-quality, customizable components that help developers build stunning websites quickly and efficiently.\",\n    vision: \"We envision a world where creating beautiful websites is accessible to everyone, regardless of their design or development experience.\",\n    values: defaultValues,\n    className: \"relative overflow-hidden py-20\"\n  };\n\n  const missionRef = useRef(null);\n  const valuesRef = useRef(null);\n\n  const missionInView = useInView(missionRef, { once: true, amount: 0.3 });\n  const valuesInView = useInView(valuesRef, { once: true, amount: 0.3 });\n\n  return (\n    <section className=\"relative overflow-hidden pt-20 w-full\">\n      <Spotlight\n        gradientFirst=\"radial-gradient(68.54% 68.72% at 55.02% 31.46%, hsla(336, 100%, 50%, 0.08) 0, hsla(341, 100%, 55%, 0.04) 50%, hsla(336, 100%, 45%, 0) 80%)\"\n        gradientSecond=\"radial-gradient(50% 50% at 50% 50%, hsla(333, 100%, 85%, 0.08) 0, hsla(335, 100%, 55%, 0.04) 80%, transparent 100%)\"\n        gradientThird=\"radial-gradient(50% 50% at 50% 50%, hsla(332, 100%, 85%, 0.06) 0, hsla(327, 100%, 85%, 0.06) 80%, transparent 100%)\"\n      />\n\n      <div className=\"container relative z-10 mx-auto px-4 md:px-6\">\n        {/* Header Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n          className=\"mx-auto mb-16 max-w-2xl text-center\"\n        >\n          <h1 className=\"bg-gradient-to-r from-foreground/80 via-foreground to-foreground/80 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl md:text-6xl\">\n            {aboutData.title}\n          </h1>\n          <p className=\"mt-6 text-xl text-muted-foreground\">{aboutData.subtitle}</p>\n        </motion.div>\n\n        {/* Mission & Vision Section */}\n        <div ref={missionRef} className=\"relative mb-24 max-w-7xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 40 }}\n            animate={\n              missionInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }\n            }\n            transition={{ duration: 0.8, delay: 0.2, ease: \"easeOut\" }}\n            className=\"relative z-10 grid gap-12 md:grid-cols-2\"\n          >\n            <motion.div\n              whileHover={{ y: -5, boxShadow: \"0 20px 40px rgba(0,0,0,0.1)\" }}\n              className=\"group block relative overflow-hidden rounded-2xl p-10 bg-gradient-to-br backdrop-blur-3xl border border-border/40\"\n            >\n              <BorderBeam\n                duration={8}\n                size={300}\n                className=\"from-transparent via-primary/40 to-transparent\"\n              />\n\n              <div className=\"mb-6 inline-flex flex-1 h-16 aspect-square w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-primary/20 to-primary/5 backdrop-blur-sm\">\n                <Rocket className=\"h-8 w-8 text-primary\" />\n              </div>\n\n              <div className=\"space-y-4\">\n                <h2 className=\"mb-4 bg-gradient-to-r from-primary/90 to-primary/70 bg-clip-text text-3xl font-bold text-transparent\">\n                  Our Mission\n                </h2>\n\n                <p className=\"text-lg leading-relaxed text-muted-foreground\">\n                  {aboutData.mission}\n                </p>\n              </div>\n            </motion.div>\n\n            <motion.div\n              whileHover={{ y: -5, boxShadow: \"0 20px 40px rgba(0,0,0,0.1)\" }}\n              className=\"group block relative overflow-hidden rounded-2xl p-10 bg-gradient-to-br backdrop-blur-3xl border border-border/40\"\n            >\n              <BorderBeam\n                duration={8}\n                size={300}\n                className=\"from-transparent via-blue-500/40 to-transparent\"\n                reverse\n              />\n              <div className=\"mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500/20 to-blue-500/5 backdrop-blur-sm\">\n                <Target className=\"h-8 w-8 text-blue-500\" />\n              </div>\n\n              <h2 className=\"mb-4 bg-gradient-to-r from-blue-500/90 to-blue-500/70 bg-clip-text text-3xl font-bold text-transparent\">\n                Our Vision\n              </h2>\n\n              <p className=\"text-lg leading-relaxed text-muted-foreground\">\n                {aboutData.vision}\n              </p>\n            </motion.div>\n          </motion.div>\n        </div>\n\n        <div ref={valuesRef} className=\"mb-24\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={\n              valuesInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }\n            }\n            transition={{ duration: 0.6, ease: \"easeOut\" }}\n            className=\"mb-12 text-center\"\n          >\n            <h2 className=\"bg-gradient-to-r from-foreground/80 via-foreground to-foreground/80 bg-clip-text text-3xl font-bold tracking-tight text-transparent sm:text-4xl\">\n              Our Core Values\n            </h2>\n            <p className=\"mx-auto mt-4 max-w-2xl text-lg text-muted-foreground\">\n              The principles that guide everything we do and every decision we\n              make.\n            </p>\n          </motion.div>\n\n          <div className=\"grid gap-6 md:grid-cols-2 xl:grid-cols-4\">\n            {aboutData.values?.map((value, index) => {\n              const IconComponent = iconComponents[value.icon];\n\n              return (\n                <motion.div\n                  key={value.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={\n                    valuesInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }\n                  }\n                  transition={{\n                    duration: 0.6,\n                    delay: index * 0.1 + 0.2,\n                    ease: \"easeOut\",\n                  }}\n                  whileHover={{ y: -5, scale: 1.02 }}\n                >\n                  <CardHoverEffect\n                    icon={<IconComponent className=\"h-6 w-6\" />}\n                    title={value.title}\n                    description={value.description}\n                    variant={\n                      index === 0\n                        ? \"purple\"\n                        : index === 1\n                          ? \"blue\"\n                          : index === 2\n                            ? \"amber\"\n                            : \"rose\"\n                    }\n                    glowEffect={true}\n                    size=\"lg\"\n                  />\n                </motion.div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}", "path": "/components/mvpblocks/mainsections/about/about-us-1.tsx", "target": "/components/mvpblocks/about-us-1.tsx"}]}