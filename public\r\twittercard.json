{"name": "twittercard", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport { VerifiedIcon } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport Link from \"next/link\";\r\n\r\ninterface ReplyProps {\r\n  authorName: string;\r\n  authorHandle: string;\r\n  authorImage: string;\r\n  content: string;\r\n  isVerified?: boolean;\r\n  timestamp: string;\r\n}\r\n\r\ninterface TweetCardProps {\r\n  authorName: string;\r\n  authorHandle: string;\r\n  authorImage: string;\r\n  content: string[];\r\n  isVerified?: boolean;\r\n  timestamp: string;\r\n  reply?: ReplyProps;\r\n}\r\n\r\nexport default function TweetCard({\r\n  authorName = \"Subhadeep\",\r\n  authorHandle = \"mvp_Subha\",\r\n  authorImage = \"https://pbs.twimg.com/profile_images/1763223695898681344/2mvSadJl_400x400.webp\",\r\n  content = [\r\n    \"Mvpblocks is the best ever UI component collection library 🎉\",\r\n    \"1. Can be opened in v0\",\r\n    \"2. Can be installed with CLI\",\r\n    \"3. Deploy to your app\",\r\n  ],\r\n  isVerified = true,\r\n  timestamp = \"Mar 3, 2025\",\r\n  reply = {\r\n    authorName: \"shadcn\",\r\n    authorHandle: \"shadcn\",\r\n    authorImage:\r\n      \"https://pbs.twimg.com/profile_images/1593304942210478080/TUYae5z7_400x400.webp\",\r\n    content: \"Awesome.\",\r\n    isVerified: true,\r\n    timestamp: \"March 3\",\r\n  },\r\n}: TweetCardProps) {\r\n  return (\r\n    <Link href=\"https://x.com/mvp_Subha\" target=\"_blank\">\r\n      <div\r\n        className={cn(\r\n          \"relative isolate w-full min-w-[400px] max-w-xl overflow-hidden rounded-2xl p-1.5 md:min-w-[500px]\",\r\n          \"bg-white/5 dark:bg-black/90\",\r\n          \"bg-linear-to-br from-black/5 to-black/[0.02] dark:from-white/5 dark:to-white/[0.02]\",\r\n          \"backdrop-blur-xl backdrop-saturate-[180%]\",\r\n          \"border border-black/10 dark:border-white/10\",\r\n          \"shadow-[0_8px_16px_rgb(0_0_0_/_0.15)] dark:shadow-[0_8px_16px_rgb(0_0_0_/_0.25)]\",\r\n          \"translate-z-0 will-change-transform\",\r\n        )}\r\n      >\r\n        <div\r\n          className={cn(\r\n            \"relative w-full rounded-xl p-5\",\r\n            \"bg-linear-to-br from-black/[0.05] to-transparent dark:from-white/[0.08] dark:to-transparent\",\r\n            \"backdrop-blur-md backdrop-saturate-150\",\r\n            \"border border-black/[0.05] dark:border-white/[0.08]\",\r\n            \"text-black/90 dark:text-white\",\r\n            \"shadow-xs\",\r\n            \"translate-z-0 will-change-transform\",\r\n            \"before:bg-linear-to-br before:pointer-events-none before:absolute before:inset-0 before:from-black/[0.02] before:to-black/[0.01] before:opacity-0 before:transition-opacity dark:before:from-white/[0.03] dark:before:to-white/[0.01]\",\r\n            \"hover:before:opacity-100\",\r\n          )}\r\n        >\r\n          <div className=\"flex gap-3\">\r\n            <div className=\"shrink-0\">\r\n              <div className=\"h-10 w-10 overflow-hidden rounded-full\">\r\n                <img\r\n                  src={authorImage}\r\n                  alt={authorName}\r\n                  className=\"h-full w-full object-cover\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex-1\">\r\n              <div className=\"flex items-start justify-between\">\r\n                <div className=\"flex flex-col\">\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <span className=\"cursor-pointer font-semibold text-black hover:underline dark:text-white/90\">\r\n                      {authorName}\r\n                    </span>\r\n                    {isVerified && (\r\n                      <VerifiedIcon className=\"h-4 w-4 text-blue-400\" />\r\n                    )}\r\n                  </div>\r\n                  <span className=\"text-sm text-black dark:text-white/60\">\r\n                    @{authorHandle}\r\n                  </span>\r\n                </div>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"flex h-8 w-8 items-center justify-center rounded-lg p-1 text-black hover:bg-black/5 hover:text-black dark:text-white/80 dark:hover:bg-white/5 dark:hover:text-white\"\r\n                >\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    width=\"1200\"\r\n                    height=\"1227\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 1200 1227\"\r\n                    className=\"h-4 w-4\"\r\n                  >\r\n                    <title>X</title>\r\n                    <path\r\n                      fill=\"currentColor\"\r\n                      d=\"M714.163 519.284 1160.89 0h-105.86L667.137 450.887 357.328 0H0l468.492 681.821L0 1226.37h105.866l409.625-476.152 327.181 476.152H1200L714.137 519.284h.026ZM569.165 687.828l-47.468-67.894-377.686-540.24h162.604l304.797 435.991 47.468 67.894 396.2 566.721H892.476L569.165 687.854v-.026Z\"\r\n                    />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"mt-2\">\r\n            {content.map((item, index) => (\r\n              <p\r\n                key={index}\r\n                className=\"text-base text-black dark:text-white/90\"\r\n              >\r\n                {item}\r\n              </p>\r\n            ))}\r\n            <span className=\"mt-2 block text-sm text-black dark:text-white/50\">\r\n              {timestamp}\r\n            </span>\r\n          </div>\r\n\r\n          {reply && (\r\n            <div className=\"mt-4 border-t border-black/[0.08] pt-4 dark:border-white/[0.08]\">\r\n              <div className=\"flex gap-3\">\r\n                <div className=\"shrink-0\">\r\n                  <div className=\"h-10 w-10 overflow-hidden rounded-full\">\r\n                    <img\r\n                      src={reply.authorImage}\r\n                      alt={reply.authorName}\r\n                      className=\"h-full w-full object-cover\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <span className=\"cursor-pointer font-semibold text-black hover:underline dark:text-white/90\">\r\n                      {reply.authorName}\r\n                    </span>\r\n                    {reply.isVerified && (\r\n                      <VerifiedIcon className=\"h-4 w-4 text-blue-400\" />\r\n                    )}\r\n                    <span className=\"text-sm text-black dark:text-white/60\">\r\n                      @{reply.authorHandle}\r\n                    </span>\r\n                    <span className=\"text-sm text-black dark:text-white/60\">\r\n                      ·\r\n                    </span>\r\n                    <span className=\"text-sm text-black dark:text-white/60\">\r\n                      {reply.timestamp}\r\n                    </span>\r\n                  </div>\r\n                  <p className=\"mt-1 text-sm text-black dark:text-white/80\">\r\n                    {reply.content}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </Link>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/cards/twitter/twittercard.tsx", "target": "/components/mvpblocks/twittercard.tsx"}]}