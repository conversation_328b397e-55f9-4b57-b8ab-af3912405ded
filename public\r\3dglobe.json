{"name": "3dglobe", "type": "registry:block", "dependencies": ["framer-motion"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { motion } from \"framer-motion\";\r\n\r\nexport default function Globe3D() {\r\n  return (\r\n    <section\r\n      className=\"relative w-full overflow-hidden bg-[#0a0613] pb-10 pt-32 font-light text-white antialiased md:pb-16 md:pt-20\"\r\n      style={{\r\n        background: \"linear-gradient(135deg, #0a0613 0%, #150d27 100%)\",\r\n      }}\r\n    >\r\n      <div\r\n        className=\"absolute right-0 top-0 h-1/2 w-1/2\"\r\n        style={{\r\n          background:\r\n            \"radial-gradient(circle at 70% 30%, rgba(155, 135, 245, 0.15) 0%, rgba(13, 10, 25, 0) 60%)\",\r\n        }}\r\n      />\r\n      <div\r\n        className=\"absolute left-0 top-0 h-1/2 w-1/2 -scale-x-100\"\r\n        style={{\r\n          background:\r\n            \"radial-gradient(circle at 70% 30%, rgba(155, 135, 245, 0.15) 0%, rgba(13, 10, 25, 0) 60%)\",\r\n        }}\r\n      />\r\n\r\n      <div className=\"container relative z-10 mx-auto max-w-2xl px-4 text-center md:max-w-4xl md:px-6 lg:max-w-7xl\">\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\r\n        >\r\n          <span className=\"mb-6 inline-block rounded-full border border-[#9b87f5]/30 px-3 py-1 text-xs text-[#9b87f5]\">\r\n            NEXT GENERATION OF CRYPTO TRADING\r\n          </span>\r\n          <h1 className=\"mx-auto mb-6 max-w-4xl text-4xl font-light md:text-5xl lg:text-7xl\">\r\n            Trade Smarter with{\" \"}\r\n            <span className=\"text-[#9b87f5]\">AI-Powered</span> Crypto Insights\r\n          </h1>\r\n          <p className=\"mx-auto mb-10 max-w-2xl text-lg text-white/60 md:text-xl\">\r\n            Lunexa combines artificial intelligence with cutting-edge trading\r\n            strategies to help you maximize your crypto investments with\r\n            precision and ease.\r\n          </p>\r\n\r\n          <div className=\"mb-10 sm:mb-0 flex flex-col items-center justify-center gap-4 sm:flex-row\">\r\n            <Link\r\n              href=\"/docs/get-started\"\r\n              className=\"neumorphic-button hover:shadow-[0_0_20px_rgba(155, 135, 245, 0.5)] relative w-full overflow-hidden rounded-full border border-white/10 bg-gradient-to-b from-white/10 to-white/5 px-8 py-4 text-white shadow-lg transition-all duration-300 hover:border-[#9b87f5]/30 sm:w-auto\"\r\n            >\r\n              Get Started\r\n            </Link>\r\n            <a\r\n              href=\"#how-it-works\"\r\n              className=\"flex w-full items-center justify-center gap-2 text-white/70 transition-colors hover:text-white sm:w-auto\"\r\n            >\r\n              <span>Learn how it works</span>\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                stroke=\"currentColor\"\r\n                strokeWidth=\"1\"\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n              >\r\n                <path d=\"m6 9 6 6 6-6\"></path>\r\n              </svg>\r\n            </a>\r\n          </div>\r\n        </motion.div>\r\n        <motion.div\r\n          className=\"relative\"\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ duration: 0.8, ease: \"easeOut\", delay: 0.3 }}\r\n        >\r\n          <div className=\"w-full flex h-40 md:h-64 relative overflow-hidden\">\r\n            <img\r\n              src=\"https://blocks.mvp-subha.me/assets/earth.png\"\r\n              alt=\"Earth\"\r\n              className=\"absolute px-4 top-0 left-1/2 -translate-x-1/2 mx-auto -z-10 opacity-80\"\r\n            />\r\n          </div>\r\n          <div className=\"relative z-10 mx-auto max-w-5xl overflow-hidden rounded-lg shadow-[0_0_50px_rgba(155,135,245,0.2)]\">\r\n            <Image\r\n              src=\"https://blocks.mvp-subha.me/assets/lunexa-db.png\"\r\n              alt=\"Lunexa Dashboard\"\r\n              width={1920}\r\n              height={1080}\r\n              className=\"h-auto w-full rounded-lg border border-white/10\"\r\n              priority\r\n            />\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/hero/3dglobe.tsx", "target": "/components/mvpblocks/3dglobe.tsx"}]}