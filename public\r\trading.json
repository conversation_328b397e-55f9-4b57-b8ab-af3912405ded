{"name": "trading", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/typewriter.json", "https://blocks.mvp-subha.me/r/border-beam.json"], "files": [{"type": "registry:block", "content": "import { ArrowRight, Command } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { motion } from \"framer-motion\";\r\nimport TextGenerateEffect from \"@/components/ui/typewriter\";\r\nimport { BorderBeam } from \"@/components/ui/border-beam\";\r\n\r\nexport default function Trading() {\r\n  return (\r\n    <motion.section\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.5 }}\r\n      className=\"container relative mx-auto max-w-6xl px-4 py-20\"\r\n    >\r\n      <motion.div\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        transition={{ delay: 0.2 }}\r\n        className=\"mb-4 inline-block w-fit rounded-full border bg-white/5 px-4 py-1.5 backdrop-blur-lg\"\r\n        style={{ boxShadow: \"0 0 10px 0 #e60a6430 inset\" }}\r\n      >\r\n        <span className=\"text-sm font-medium\">\r\n          <Command className=\"mr-2 inline-block h-4 w-4\" />\r\n          Next-gen crypto trading platform\r\n        </span>\r\n      </motion.div>\r\n\r\n      <div className=\"relative z-10 mt-6\">\r\n        <h1 className=\"mb-4 text-left text-5xl font-normal tracking-tight md:text-7xl\">\r\n          <span className=\"text-foreground\">\r\n            <TextGenerateEffect words=\"Trade crypto with\" />\r\n          </span>\r\n          <br />\r\n          <span className=\"font-medium text-foreground\">\r\n            <TextGenerateEffect words=\"confidence & security\" />\r\n          </span>\r\n        </h1>\r\n\r\n        <motion.p\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.4 }}\r\n          className=\"mb-8 max-w-2xl text-left text-lg text-foreground/50 md:text-xl\"\r\n        >\r\n          Experience seamless cryptocurrency trading with advanced features,\r\n          real-time analytics, and institutional-grade security.{\" \"}\r\n          <span className=\"text-foreground/50\">Start trading in minutes.</span>\r\n        </motion.p>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.5 }}\r\n          className=\"flex flex-col items-start gap-4 sm:flex-row\"\r\n        >\r\n          <Button\r\n            size=\"lg\"\r\n            className=\"rounded-full bg-gradient-to-b from-rose-500 to-rose-700 text-white shadow-[0px_2px_0px_0px_rgba(255,255,255,0.3)_inset]\"\r\n          >\r\n            Start Trading Now\r\n          </Button>\r\n          <Button size=\"lg\" variant=\"link\">\r\n            View Markets <ArrowRight className=\"ml-2 h-4 w-4\" />\r\n          </Button>\r\n        </motion.div>\r\n      </div>\r\n\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.6 }}\r\n        className=\"rounded-xl relative mx-auto mt-20 max-w-5xl overflow-hidden\"\r\n      >\r\n        <div className=\"overflow-hidden rounded-xl\">\r\n          <img\r\n            src=\"https://blocks.mvp-subha.me/assets/trading-hero/db.jpg\"\r\n            alt=\"CryptoTrade Dashboard\"\r\n            className=\"h-auto w-full\"\r\n          />\r\n        </div>\r\n        <BorderBeam\r\n          duration={6}\r\n          size={400}\r\n          className=\"from-transparent via-red-500 to-transparent\"\r\n        />\r\n        <BorderBeam\r\n          duration={6}\r\n          delay={3}\r\n          size={400}\r\n          className=\"from-transparent via-blue-500 to-transparent\"\r\n        />\r\n      </motion.div>\r\n    </motion.section>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/hero/trading.tsx", "target": "/components/mvpblocks/trading.tsx"}]}