{"name": "spiral-loader", "type": "registry:block", "dependencies": ["framer-motion"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "import { motion } from \"framer-motion\";\r\n\r\nexport default function SpiralLoader() {\r\n  const dots = 8;\r\n  const radius = 20;\r\n\r\n  return (\r\n    <div className=\"relative h-16 w-16\">\r\n      {[...Array(dots)].map((_, index) => {\r\n        const angle = (index / dots) * (2 * Math.PI);\r\n        const x = radius * Math.cos(angle);\r\n        const y = radius * Math.sin(angle);\r\n\r\n        return (\r\n          <motion.div\r\n            key={index}\r\n            className=\"absolute h-3 w-3 rounded-full bg-red-500\"\r\n            style={{\r\n              left: `calc(50% + ${x}px)`,\r\n              top: `calc(50% + ${y}px)`,\r\n            }}\r\n            animate={{\r\n              scale: [0, 1, 0],\r\n              opacity: [0, 1, 0],\r\n            }}\r\n            transition={{\r\n              duration: 1.5,\r\n              repeat: Infinity,\r\n              delay: (index / dots) * 1.5,\r\n              ease: \"easeInOut\",\r\n            }}\r\n          />\r\n        );\r\n      })}\r\n    </div>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/basics/loaders/spiral-loader.tsx", "target": "/components/mvpblocks/spiral-loader.tsx"}]}