{"name": "notebook", "type": "registry:block", "dependencies": ["lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport { buttonVariants } from \"@/components/ui/button\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\n\r\nexport default function NotebookHero() {\r\n  return (\r\n    <div className=\"min-h-screen py-6 sm:py-14\">\r\n      {/* Add keyframes for the animation */}\r\n      <style jsx global>{`\r\n        @keyframes moveGradientLeft {\r\n          0% {\r\n            background-position: 0% 0%;\r\n          }\r\n          100% {\r\n            background-position: -200% 0%;\r\n          }\r\n        }\r\n        .animate-gradient-x {\r\n          animation: moveGradientLeft 20s linear infinite;\r\n        }\r\n      `}</style>\r\n\r\n      <div className=\"pointer-events-none absolute inset-0 top-0 z-0 overflow-hidden\">\r\n        <div className=\"absolute -left-20 -top-20 h-[600px] w-[600px] rounded-full bg-gradient-to-br from-rose-500/30 via-rose-500/20 to-transparent opacity-50 blur-[100px]\" />\r\n        <div className=\"absolute -right-20 -top-40 h-[500px] w-[500px] rounded-full bg-gradient-to-bl from-red-500/30 via-red-500/20 to-transparent opacity-50 blur-[100px]\" />\r\n        <div className=\"absolute bottom-0 left-0 h-[400px] w-[400px] rounded-full bg-gradient-to-tr from-amber-500/20 via-amber-500/10 to-transparent opacity-30 blur-[80px]\" />\r\n      </div>\r\n\r\n      <main className=\"container relative mt-4 max-w-[1100px] px-2 py-4 lg:py-8\">\r\n        <div className=\"relative sm:overflow-hidden\">\r\n          <div className=\"relative flex flex-col items-start justify-start rounded-xl border border-primary/20 bg-fd-background/70 px-4 pt-12 shadow-xl shadow-primary/10 backdrop-blur-md max-md:text-center md:px-12 md:pt-16\">\r\n            <div\r\n              className=\"animate-gradient-x absolute inset-0 top-32 z-0 hidden blur-2xl dark:block\"\r\n              style={{\r\n                maskImage:\r\n                  \"linear-gradient(to bottom, transparent, white, transparent)\",\r\n                background:\r\n                  \"repeating-linear-gradient(65deg, hsl(var(--primary)), hsl(var(--primary)/0.8) 12px, color-mix(in oklab, hsl(var(--primary)) 30%, transparent) 20px, transparent 200px)\",\r\n                backgroundSize: \"200% 100%\",\r\n              }}\r\n            />\r\n            <div\r\n              className=\"animate-gradient-x absolute inset-0 top-32 z-0 text-left blur-2xl dark:hidden\"\r\n              style={{\r\n                maskImage:\r\n                  \"linear-gradient(to bottom, transparent, white, transparent)\",\r\n                background:\r\n                  \"repeating-linear-gradient(65deg, hsl(var(--primary)/0.9), hsl(var(--primary)/0.7) 12px, color-mix(in oklab, hsl(var(--primary)) 30%, transparent) 20px, transparent 200px)\",\r\n                backgroundSize: \"200% 100%\",\r\n              }}\r\n            />\r\n            <h1 className=\"mb-4 flex flex-wrap gap-2 text-3xl font-medium leading-tight md:text-5xl\">\r\n              Build <span className=\"text-primary\">Beautiful UI</span> with\r\n              MVPBlocks\r\n            </h1>\r\n            <p className=\"mb-8 text-left text-muted-foreground md:max-w-[80%] md:text-xl\">\r\n              Your comprehensive library of ready-to-use UI components built\r\n              with Next.js and Tailwind CSS. From simple buttons to complex\r\n              layouts, MVPBlocks helps you create stunning interfaces with\r\n              minimal effort.\r\n            </p>\r\n            <div className=\"mb-6 flex flex-wrap gap-4 md:flex-row\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <svg\r\n                  className=\"h-5 w-5 text-primary\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth=\"2\"\r\n                    d=\"M5 13l4 4L19 7\"\r\n                  ></path>\r\n                </svg>\r\n                <span>100+ Components</span>\r\n              </div>\r\n              <div className=\"flex items-center gap-2\">\r\n                <svg\r\n                  className=\"h-5 w-5 text-primary\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth=\"2\"\r\n                    d=\"M5 13l4 4L19 7\"\r\n                  ></path>\r\n                </svg>\r\n                <span>Dark & Light Mode</span>\r\n              </div>\r\n              <div className=\"flex items-center gap-2\">\r\n                <svg\r\n                  className=\"h-5 w-5 text-primary\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth=\"2\"\r\n                    d=\"M5 13l4 4L19 7\"\r\n                  ></path>\r\n                </svg>\r\n                <span>Responsive Design</span>\r\n              </div>\r\n              <div className=\"flex items-center gap-2\">\r\n                <svg\r\n                  className=\"h-5 w-5 text-primary\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth=\"2\"\r\n                    d=\"M5 13l4 4L19 7\"\r\n                  ></path>\r\n                </svg>\r\n                <span>Accessible Components</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"z-10 mt-2 inline-flex items-center justify-start gap-3\">\r\n              <a\r\n                href=\"#\"\r\n                className={cn(\r\n                  buttonVariants({\r\n                    size: \"lg\",\r\n                    className:\r\n                      \"rounded-full bg-gradient-to-b from-primary to-primary/80 text-primary-foreground\",\r\n                  }),\r\n                )}\r\n              >\r\n                Getting Started <ArrowRight className=\"size-4\" />\r\n              </a>\r\n              <a\r\n                href=\"https://github.com/subhadeeproy3902/mvpblocks\"\r\n                target=\"_blank\"\r\n                rel=\"noreferrer noopener\"\r\n                className={cn(\r\n                  buttonVariants({\r\n                    size: \"lg\",\r\n                    variant: \"outline\",\r\n                    className: \"rounded-full bg-fd-background\",\r\n                  }),\r\n                )}\r\n              >\r\n                GitHub{\" \"}\r\n                <svg\r\n                  className=\"ml-1 inline size-4\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  width=\"24\"\r\n                  height=\"24\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"2\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                >\r\n                  <path d=\"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4\" />\r\n                  <path d=\"M9 18c-4.51 2-5-2-7-2\" />\r\n                </svg>\r\n              </a>\r\n            </div>\r\n\r\n            <div className=\"relative z-10 mt-16 w-full\">\r\n              <Image\r\n                src=\"https://blocks.mvp-subha.me/assets/bg.png\"\r\n                alt=\"MVPBlocks component library preview\"\r\n                width={1000}\r\n                height={600}\r\n                priority\r\n                className=\"border-6 z-10 mx-auto -mb-60 w-full select-none rounded-lg border-neutral-100 object-cover shadow-2xl duration-1000 animate-in fade-in slide-in-from-bottom-12 dark:border-neutral-600 lg:-mb-40\"\r\n              />\r\n\r\n              <div className=\"absolute -right-6 -top-6 rotate-6 transform rounded-lg bg-white p-3 shadow-lg animate-in fade-in slide-in-from-left-4 dark:bg-neutral-900\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <svg\r\n                    className=\"h-5 w-5 text-green-500\"\r\n                    fill=\"currentColor\"\r\n                    viewBox=\"0 0 20 20\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      fillRule=\"evenodd\"\r\n                      d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\"\r\n                      clipRule=\"evenodd\"\r\n                    ></path>\r\n                  </svg>\r\n                  <span className=\"font-medium\">Ready-to-Use Components</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </main>\r\n    </div>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/hero/notebook.tsx", "target": "/components/mvpblocks/notebook.tsx"}]}