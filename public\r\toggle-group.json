{"name": "toggle-group", "type": "registry:ui", "dependencies": ["@radix-ui/react-toggle-group"], "registryDependencies": ["toggle"], "files": [{"type": "registry:ui", "content": "\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as ToggleGroupPrimitive from \"@radix-ui/react-toggle-group\";\r\nimport { type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { toggleVariants } from \"@/components/ui/toggle\";\r\n\r\nconst ToggleGroupContext = React.createContext<\r\n  VariantProps<typeof toggleVariants>\r\n>({\r\n  size: \"default\",\r\n  variant: \"default\",\r\n});\r\n\r\nconst ToggleGroup = React.forwardRef<\r\n  React.ElementRef<typeof ToggleGroupPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Root> &\r\n    VariantProps<typeof toggleVariants>\r\n>(({ className, variant, size, children, ...props }, ref) => (\r\n  <ToggleGroupPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"flex items-center justify-center gap-1\", className)}\r\n    {...props}\r\n  >\r\n    <ToggleGroupContext.Provider value={{ variant, size }}>\r\n      {children}\r\n    </ToggleGroupContext.Provider>\r\n  </ToggleGroupPrimitive.Root>\r\n));\r\n\r\nToggleGroup.displayName = ToggleGroupPrimitive.Root.displayName;\r\n\r\nconst ToggleGroupItem = React.forwardRef<\r\n  React.ElementRef<typeof ToggleGroupPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Item> &\r\n    VariantProps<typeof toggleVariants>\r\n>(({ className, children, variant, size, ...props }, ref) => {\r\n  const context = React.useContext(ToggleGroupContext);\r\n\r\n  return (\r\n    <ToggleGroupPrimitive.Item\r\n      ref={ref}\r\n      className={cn(\r\n        toggleVariants({\r\n          variant: context.variant || variant,\r\n          size: context.size || size,\r\n        }),\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </ToggleGroupPrimitive.Item>\r\n  );\r\n});\r\n\r\nToggleGroupItem.displayName = ToggleGroupPrimitive.Item.displayName;\r\n\r\nexport { ToggleGroup, ToggleGroupItem };\r\n", "path": "/components/ui/toggle-group.tsx", "target": "/components/ui/toggle-group.tsx"}]}