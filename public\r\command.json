{"name": "command", "type": "registry:ui", "dependencies": ["cmdk"], "registryDependencies": ["dialog"], "files": [{"type": "registry:ui", "content": "\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { type DialogProps } from \"@radix-ui/react-dialog\";\r\nimport { Command as CommandPrimitive } from \"cmdk\";\r\nimport { Search } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Dialog, DialogContent } from \"@/components/ui/dialog\";\r\n\r\nconst Command = React.forwardRef<\r\n  React.ElementRef<typeof CommandPrimitive>,\r\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive>\r\n>(({ className, ...props }, ref) => (\r\n  <CommandPrimitive\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nCommand.displayName = CommandPrimitive.displayName;\r\n\r\nconst CommandDialog = ({ children, ...props }: DialogProps) => {\r\n  return (\r\n    <Dialog {...props}>\r\n      <DialogContent className=\"overflow-hidden p-0 shadow-lg\">\r\n        <Command className=\"[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\r\n          {children}\r\n        </Command>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nconst CommandInput = React.forwardRef<\r\n  React.ElementRef<typeof CommandPrimitive.Input>,\r\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Input>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"flex items-center border-b px-3\" cmdk-input-wrapper=\"\">\r\n    <Search className=\"mr-2 h-4 w-4 shrink-0 opacity-50\" />\r\n    <CommandPrimitive.Input\r\n      ref={ref}\r\n      className={cn(\r\n        \"flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  </div>\r\n));\r\n\r\nCommandInput.displayName = CommandPrimitive.Input.displayName;\r\n\r\nconst CommandList = React.forwardRef<\r\n  React.ElementRef<typeof CommandPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <CommandPrimitive.List\r\n    ref={ref}\r\n    className={cn(\"max-h-[300px] overflow-y-auto overflow-x-hidden\", className)}\r\n    {...props}\r\n  />\r\n));\r\n\r\nCommandList.displayName = CommandPrimitive.List.displayName;\r\n\r\nconst CommandEmpty = React.forwardRef<\r\n  React.ElementRef<typeof CommandPrimitive.Empty>,\r\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Empty>\r\n>((props, ref) => (\r\n  <CommandPrimitive.Empty\r\n    ref={ref}\r\n    className=\"py-6 text-center text-sm\"\r\n    {...props}\r\n  />\r\n));\r\n\r\nCommandEmpty.displayName = CommandPrimitive.Empty.displayName;\r\n\r\nconst CommandGroup = React.forwardRef<\r\n  React.ElementRef<typeof CommandPrimitive.Group>,\r\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Group>\r\n>(({ className, ...props }, ref) => (\r\n  <CommandPrimitive.Group\r\n    ref={ref}\r\n    className={cn(\r\n      \"overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\n\r\nCommandGroup.displayName = CommandPrimitive.Group.displayName;\r\n\r\nconst CommandSeparator = React.forwardRef<\r\n  React.ElementRef<typeof CommandPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <CommandPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 h-px bg-border\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCommandSeparator.displayName = CommandPrimitive.Separator.displayName;\r\n\r\nconst CommandItem = React.forwardRef<\r\n  React.ElementRef<typeof CommandPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Item>\r\n>(({ className, ...props }, ref) => (\r\n  <CommandPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\n\r\nCommandItem.displayName = CommandPrimitive.Item.displayName;\r\n\r\nconst CommandShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\r\n        \"ml-auto text-xs tracking-widest text-muted-foreground\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n};\r\nCommandShortcut.displayName = \"CommandShortcut\";\r\n\r\nexport {\r\n  Command,\r\n  CommandDialog,\r\n  CommandInput,\r\n  CommandList,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandShortcut,\r\n  CommandSeparator,\r\n};\r\n", "path": "/components/ui/command.tsx", "target": "/components/ui/command.tsx"}]}