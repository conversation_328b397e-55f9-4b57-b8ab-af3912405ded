{"name": "animated-ai-chat", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport { useEffect, useRef, useCallback, useTransition } from \"react\";\r\nimport { useState } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  ImageIcon,\r\n  Figma,\r\n  MonitorIcon,\r\n  Paperclip,\r\n  SendIcon,\r\n  XIcon,\r\n  LoaderIcon,\r\n  Sparkles,\r\n  Command,\r\n} from \"lucide-react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport * as React from \"react\";\r\n\r\ninterface UseAutoResizeTextareaProps {\r\n  minHeight: number;\r\n  maxHeight?: number;\r\n}\r\n\r\nfunction useAutoResizeTextarea({\r\n  minHeight,\r\n  maxHeight,\r\n}: UseAutoResizeTextareaProps) {\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n\r\n  const adjustHeight = useCallback(\r\n    (reset?: boolean) => {\r\n      const textarea = textareaRef.current;\r\n      if (!textarea) return;\r\n\r\n      if (reset) {\r\n        textarea.style.height = `${minHeight}px`;\r\n        return;\r\n      }\r\n\r\n      textarea.style.height = `${minHeight}px`;\r\n      const newHeight = Math.max(\r\n        minHeight,\r\n        Math.min(textarea.scrollHeight, maxHeight ?? Number.POSITIVE_INFINITY),\r\n      );\r\n\r\n      textarea.style.height = `${newHeight}px`;\r\n    },\r\n    [minHeight, maxHeight],\r\n  );\r\n\r\n  useEffect(() => {\r\n    const textarea = textareaRef.current;\r\n    if (textarea) {\r\n      textarea.style.height = `${minHeight}px`;\r\n    }\r\n  }, [minHeight]);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => adjustHeight();\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, [adjustHeight]);\r\n\r\n  return { textareaRef, adjustHeight };\r\n}\r\n\r\ninterface CommandSuggestion {\r\n  icon: React.ReactNode;\r\n  label: string;\r\n  description: string;\r\n  prefix: string;\r\n}\r\n\r\ninterface TextareaProps\r\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\r\n  containerClassName?: string;\r\n  showRing?: boolean;\r\n}\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\r\n  ({ className, containerClassName, showRing = true, ...props }, ref) => {\r\n    const [isFocused, setIsFocused] = React.useState(false);\r\n\r\n    return (\r\n      <div className={cn(\"relative\", containerClassName)}>\r\n        <textarea\r\n          className={cn(\r\n            \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm\",\r\n            \"transition-all duration-200 ease-in-out\",\r\n            \"placeholder:text-muted-foreground\",\r\n            \"disabled:cursor-not-allowed disabled:opacity-50\",\r\n            showRing\r\n              ? \"focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0\"\r\n              : \"\",\r\n            className,\r\n          )}\r\n          ref={ref}\r\n          onFocus={() => setIsFocused(true)}\r\n          onBlur={() => setIsFocused(false)}\r\n          {...props}\r\n        />\r\n\r\n        {showRing && isFocused && (\r\n          <motion.span\r\n            className=\"pointer-events-none absolute inset-0 rounded-md ring-2 ring-primary/30 ring-offset-0\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            transition={{ duration: 0.2 }}\r\n          />\r\n        )}\r\n\r\n        {props.onChange && (\r\n          <div\r\n            className=\"absolute bottom-2 right-2 h-2 w-2 rounded-full bg-primary opacity-0\"\r\n            style={{\r\n              animation: \"none\",\r\n            }}\r\n            id=\"textarea-ripple\"\r\n          />\r\n        )}\r\n      </div>\r\n    );\r\n  },\r\n);\r\nTextarea.displayName = \"Textarea\";\r\n\r\nexport default function AnimatedAIChat() {\r\n  const [value, setValue] = useState(\"\");\r\n  const [attachments, setAttachments] = useState<string[]>([]);\r\n  const [isTyping, setIsTyping] = useState(false);\r\n  const [isPending, startTransition] = useTransition();\r\n  const [activeSuggestion, setActiveSuggestion] = useState<number>(-1);\r\n  const [showCommandPalette, setShowCommandPalette] = useState(false);\r\n  const [recentCommand, setRecentCommand] = useState<string | null>(null);\r\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\r\n  const { textareaRef, adjustHeight } = useAutoResizeTextarea({\r\n    minHeight: 60,\r\n    maxHeight: 200,\r\n  });\r\n  const [inputFocused, setInputFocused] = useState(false);\r\n  const commandPaletteRef = useRef<HTMLDivElement>(null);\r\n\r\n  const commandSuggestions: CommandSuggestion[] = [\r\n    {\r\n      icon: <ImageIcon className=\"h-4 w-4\" />,\r\n      label: \"Clone UI\",\r\n      description: \"Generate a UI from a screenshot\",\r\n      prefix: \"/clone\",\r\n    },\r\n    {\r\n      icon: <Figma className=\"h-4 w-4\" />,\r\n      label: \"Import Figma\",\r\n      description: \"Import a design from Figma\",\r\n      prefix: \"/figma\",\r\n    },\r\n    {\r\n      icon: <MonitorIcon className=\"h-4 w-4\" />,\r\n      label: \"Create Page\",\r\n      description: \"Generate a new web page\",\r\n      prefix: \"/page\",\r\n    },\r\n    {\r\n      icon: <Sparkles className=\"h-4 w-4\" />,\r\n      label: \"Improve\",\r\n      description: \"Improve existing UI design\",\r\n      prefix: \"/improve\",\r\n    },\r\n  ];\r\n\r\n  useEffect(() => {\r\n    if (value.startsWith(\"/\") && !value.includes(\" \")) {\r\n      setShowCommandPalette(true);\r\n\r\n      const matchingSuggestionIndex = commandSuggestions.findIndex((cmd) =>\r\n        cmd.prefix.startsWith(value),\r\n      );\r\n\r\n      if (matchingSuggestionIndex >= 0) {\r\n        setActiveSuggestion(matchingSuggestionIndex);\r\n      } else {\r\n        setActiveSuggestion(-1);\r\n      }\r\n    } else {\r\n      setShowCommandPalette(false);\r\n    }\r\n  }, [value]);\r\n\r\n  useEffect(() => {\r\n    const handleMouseMove = (e: MouseEvent) => {\r\n      setMousePosition({ x: e.clientX, y: e.clientY });\r\n    };\r\n\r\n    window.addEventListener(\"mousemove\", handleMouseMove);\r\n    return () => {\r\n      window.removeEventListener(\"mousemove\", handleMouseMove);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      const target = event.target as Node;\r\n      const commandButton = document.querySelector(\"[data-command-button]\");\r\n\r\n      if (\r\n        commandPaletteRef.current &&\r\n        !commandPaletteRef.current.contains(target) &&\r\n        !commandButton?.contains(target)\r\n      ) {\r\n        setShowCommandPalette(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {\r\n    if (showCommandPalette) {\r\n      if (e.key === \"ArrowDown\") {\r\n        e.preventDefault();\r\n        setActiveSuggestion((prev) =>\r\n          prev < commandSuggestions.length - 1 ? prev + 1 : 0,\r\n        );\r\n      } else if (e.key === \"ArrowUp\") {\r\n        e.preventDefault();\r\n        setActiveSuggestion((prev) =>\r\n          prev > 0 ? prev - 1 : commandSuggestions.length - 1,\r\n        );\r\n      } else if (e.key === \"Tab\" || e.key === \"Enter\") {\r\n        e.preventDefault();\r\n        if (activeSuggestion >= 0) {\r\n          const selectedCommand = commandSuggestions[activeSuggestion];\r\n          setValue(selectedCommand.prefix + \" \");\r\n          setShowCommandPalette(false);\r\n\r\n          setRecentCommand(selectedCommand.label);\r\n          setTimeout(() => setRecentCommand(null), 3500);\r\n        }\r\n      } else if (e.key === \"Escape\") {\r\n        e.preventDefault();\r\n        setShowCommandPalette(false);\r\n      }\r\n    } else if (e.key === \"Enter\" && !e.shiftKey) {\r\n      e.preventDefault();\r\n      if (value.trim()) {\r\n        handleSendMessage();\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSendMessage = () => {\r\n    if (value.trim()) {\r\n      startTransition(() => {\r\n        setIsTyping(true);\r\n        setTimeout(() => {\r\n          setIsTyping(false);\r\n          setValue(\"\");\r\n          adjustHeight(true);\r\n        }, 3000);\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleAttachFile = () => {\r\n    const mockFileName = `file-${Math.floor(Math.random() * 1000)}.pdf`;\r\n    setAttachments((prev) => [...prev, mockFileName]);\r\n  };\r\n\r\n  const removeAttachment = (index: number) => {\r\n    setAttachments((prev) => prev.filter((_, i) => i !== index));\r\n  };\r\n\r\n  const selectCommandSuggestion = (index: number) => {\r\n    const selectedCommand = commandSuggestions[index];\r\n    setValue(selectedCommand.prefix + \" \");\r\n    setShowCommandPalette(false);\r\n\r\n    setRecentCommand(selectedCommand.label);\r\n    setTimeout(() => setRecentCommand(null), 2000);\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex w-screen overflow-x-hidden\">\r\n      <div className=\"relative flex min-h-screen w-full flex-col items-center justify-center overflow-hidden bg-transparent p-6 text-foreground\">\r\n        <div className=\"absolute inset-0 h-full w-full overflow-hidden\">\r\n          <div className=\"absolute left-1/4 top-0 h-96 w-96 animate-pulse rounded-full bg-primary/10 mix-blend-normal blur-[128px] filter\" />\r\n          <div className=\"absolute bottom-0 right-1/4 h-96 w-96 animate-pulse rounded-full bg-secondary/10 mix-blend-normal blur-[128px] filter delay-700\" />\r\n          <div className=\"absolute right-1/3 top-1/4 h-64 w-64 animate-pulse rounded-full bg-primary/10 mix-blend-normal blur-[96px] filter delay-1000\" />\r\n        </div>\r\n        <div className=\"relative mx-auto w-full max-w-2xl\">\r\n          <motion.div\r\n            className=\"relative z-10 space-y-12\"\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, ease: \"easeOut\" }}\r\n          >\r\n            <div className=\"space-y-3 text-center\">\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 10 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.2, duration: 0.5 }}\r\n                className=\"inline-block\"\r\n              >\r\n                <h1 className=\"pb-1 text-3xl font-medium tracking-tight\">\r\n                  How can I help today?\r\n                </h1>\r\n                <motion.div\r\n                  className=\"h-px bg-gradient-to-r from-transparent via-primary/50 to-transparent\"\r\n                  initial={{ width: 0, opacity: 0 }}\r\n                  animate={{ width: \"100%\", opacity: 1 }}\r\n                  transition={{ delay: 0.5, duration: 0.8 }}\r\n                />\r\n              </motion.div>\r\n              <motion.p\r\n                className=\"text-sm text-muted-foreground\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ delay: 0.3 }}\r\n              >\r\n                Type a command or ask a question\r\n              </motion.p>\r\n            </div>\r\n\r\n            <motion.div\r\n              className=\"relative rounded-2xl border border-border bg-card/80 shadow-2xl backdrop-blur-2xl\"\r\n              initial={{ scale: 0.98 }}\r\n              animate={{ scale: 1 }}\r\n              transition={{ delay: 0.1 }}\r\n            >\r\n              <AnimatePresence>\r\n                {showCommandPalette && (\r\n                  <motion.div\r\n                    ref={commandPaletteRef}\r\n                    className=\"absolute bottom-full left-4 right-4 z-50 mb-2 overflow-hidden rounded-lg border border-border bg-background/90 shadow-lg backdrop-blur-xl\"\r\n                    initial={{ opacity: 0, y: 5 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    exit={{ opacity: 0, y: 5 }}\r\n                    transition={{ duration: 0.15 }}\r\n                  >\r\n                    <div className=\"bg-background py-1\">\r\n                      {commandSuggestions.map((suggestion, index) => (\r\n                        <motion.div\r\n                          key={suggestion.prefix}\r\n                          className={cn(\r\n                            \"flex cursor-pointer items-center gap-2 px-3 py-2 text-xs transition-colors\",\r\n                            activeSuggestion === index\r\n                              ? \"bg-primary/20 text-foreground\"\r\n                              : \"text-muted-foreground hover:bg-primary/10\",\r\n                          )}\r\n                          onClick={() => selectCommandSuggestion(index)}\r\n                          initial={{ opacity: 0 }}\r\n                          animate={{ opacity: 1 }}\r\n                          transition={{ delay: index * 0.03 }}\r\n                        >\r\n                          <div className=\"flex h-5 w-5 items-center justify-center text-primary\">\r\n                            {suggestion.icon}\r\n                          </div>\r\n                          <div className=\"font-medium\">{suggestion.label}</div>\r\n                          <div className=\"ml-1 text-xs text-muted-foreground\">\r\n                            {suggestion.prefix}\r\n                          </div>\r\n                        </motion.div>\r\n                      ))}\r\n                    </div>\r\n                  </motion.div>\r\n                )}\r\n              </AnimatePresence>\r\n\r\n              <div className=\"p-4\">\r\n                <Textarea\r\n                  ref={textareaRef}\r\n                  value={value}\r\n                  onChange={(e) => {\r\n                    setValue(e.target.value);\r\n                    adjustHeight();\r\n                  }}\r\n                  onKeyDown={handleKeyDown}\r\n                  onFocus={() => setInputFocused(true)}\r\n                  onBlur={() => setInputFocused(false)}\r\n                  placeholder=\"Ask mvp.ai a question...\"\r\n                  containerClassName=\"w-full\"\r\n                  className={cn(\r\n                    \"w-full px-4 py-3\",\r\n                    \"resize-none\",\r\n                    \"bg-transparent\",\r\n                    \"border-none\",\r\n                    \"text-sm text-foreground\",\r\n                    \"focus:outline-none\",\r\n                    \"placeholder:text-muted-foreground\",\r\n                    \"min-h-[60px]\",\r\n                  )}\r\n                  style={{\r\n                    overflow: \"hidden\",\r\n                  }}\r\n                  showRing={false}\r\n                />\r\n              </div>\r\n\r\n              <AnimatePresence>\r\n                {attachments.length > 0 && (\r\n                  <motion.div\r\n                    className=\"flex flex-wrap gap-2 px-4 pb-3\"\r\n                    initial={{ opacity: 0, height: 0 }}\r\n                    animate={{ opacity: 1, height: \"auto\" }}\r\n                    exit={{ opacity: 0, height: 0 }}\r\n                  >\r\n                    {attachments.map((file, index) => (\r\n                      <motion.div\r\n                        key={index}\r\n                        className=\"flex items-center gap-2 rounded-lg bg-primary/5 px-3 py-1.5 text-xs text-muted-foreground\"\r\n                        initial={{ opacity: 0, scale: 0.9 }}\r\n                        animate={{ opacity: 1, scale: 1 }}\r\n                        exit={{ opacity: 0, scale: 0.9 }}\r\n                      >\r\n                        <span>{file}</span>\r\n                        <button\r\n                          onClick={() => removeAttachment(index)}\r\n                          className=\"text-muted-foreground transition-colors hover:text-foreground\"\r\n                        >\r\n                          <XIcon className=\"h-3 w-3\" />\r\n                        </button>\r\n                      </motion.div>\r\n                    ))}\r\n                  </motion.div>\r\n                )}\r\n              </AnimatePresence>\r\n\r\n              <div className=\"flex items-center justify-between gap-4 border-t border-border p-4\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <motion.button\r\n                    type=\"button\"\r\n                    onClick={handleAttachFile}\r\n                    whileTap={{ scale: 0.94 }}\r\n                    className=\"group relative rounded-lg p-2 text-muted-foreground transition-colors hover:text-foreground\"\r\n                  >\r\n                    <Paperclip className=\"h-4 w-4\" />\r\n                    <motion.span\r\n                      className=\"absolute inset-0 rounded-lg bg-primary/10 opacity-0 transition-opacity group-hover:opacity-100\"\r\n                      layoutId=\"button-highlight\"\r\n                    />\r\n                  </motion.button>\r\n                  <motion.button\r\n                    type=\"button\"\r\n                    data-command-button\r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                      setShowCommandPalette((prev) => !prev);\r\n                    }}\r\n                    whileTap={{ scale: 0.94 }}\r\n                    className={cn(\r\n                      \"group relative rounded-lg p-2 text-muted-foreground transition-colors hover:text-foreground\",\r\n                      showCommandPalette && \"bg-primary/20 text-foreground\",\r\n                    )}\r\n                  >\r\n                    <Command className=\"h-4 w-4\" />\r\n                    <motion.span\r\n                      className=\"absolute inset-0 rounded-lg bg-primary/10 opacity-0 transition-opacity group-hover:opacity-100\"\r\n                      layoutId=\"button-highlight\"\r\n                    />\r\n                  </motion.button>\r\n                </div>\r\n\r\n                <motion.button\r\n                  type=\"button\"\r\n                  onClick={handleSendMessage}\r\n                  whileHover={{ scale: 1.01 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                  disabled={isTyping || !value.trim()}\r\n                  className={cn(\r\n                    \"rounded-lg px-4 py-2 text-sm font-medium transition-all\",\r\n                    \"flex items-center gap-2\",\r\n                    value.trim()\r\n                      ? \"bg-primary text-primary-foreground shadow-lg shadow-primary/10\"\r\n                      : \"bg-muted/50 text-muted-foreground\",\r\n                  )}\r\n                >\r\n                  {isTyping ? (\r\n                    <LoaderIcon className=\"h-4 w-4 animate-[spin_2s_linear_infinite]\" />\r\n                  ) : (\r\n                    <SendIcon className=\"h-4 w-4\" />\r\n                  )}\r\n                  <span>Send</span>\r\n                </motion.button>\r\n              </div>\r\n            </motion.div>\r\n\r\n            <div className=\"flex flex-wrap items-center justify-center gap-2\">\r\n              {commandSuggestions.map((suggestion, index) => (\r\n                <motion.button\r\n                  key={suggestion.prefix}\r\n                  onClick={() => selectCommandSuggestion(index)}\r\n                  className=\"group relative flex items-center gap-2 rounded-lg bg-primary/5 px-3 py-2 text-sm text-muted-foreground transition-all hover:bg-primary/10 hover:text-foreground\"\r\n                  initial={{ opacity: 0, y: 10 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: index * 0.1 }}\r\n                >\r\n                  {suggestion.icon}\r\n                  <span>{suggestion.label}</span>\r\n                  <motion.div\r\n                    className=\"absolute inset-0 rounded-lg border border-border/50\"\r\n                    initial={false}\r\n                    animate={{\r\n                      opacity: [0, 1],\r\n                      scale: [0.98, 1],\r\n                    }}\r\n                    transition={{\r\n                      duration: 0.3,\r\n                      ease: \"easeOut\",\r\n                    }}\r\n                  />\r\n                </motion.button>\r\n              ))}\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n\r\n        <AnimatePresence>\r\n          {isTyping && (\r\n            <motion.div\r\n              className=\"fixed bottom-8 mx-auto -translate-x-1/2 transform rounded-full border border-border bg-background/80 px-4 py-2 shadow-lg backdrop-blur-2xl\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: 20 }}\r\n            >\r\n              <div className=\"flex items-center gap-3\">\r\n                <div className=\"flex h-7 w-8 items-center justify-center rounded-full bg-primary/10 text-center\">\r\n                  <Sparkles className=\"h-4 w-4 text-primary\" />\r\n                </div>\r\n                <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\r\n                  <span>Thinking</span>\r\n                  <TypingDots />\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n\r\n        {inputFocused && (\r\n          <motion.div\r\n            className=\"pointer-events-none fixed z-0 h-[50rem] w-[50rem] rounded-full bg-gradient-to-r from-primary via-primary/80 to-secondary opacity-[0.02] blur-[96px]\"\r\n            animate={{\r\n              x: mousePosition.x - 400,\r\n              y: mousePosition.y - 400,\r\n            }}\r\n            transition={{\r\n              type: \"spring\",\r\n              damping: 25,\r\n              stiffness: 150,\r\n              mass: 0.5,\r\n            }}\r\n          />\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction TypingDots() {\r\n  return (\r\n    <div className=\"ml-1 flex items-center\">\r\n      {[1, 2, 3].map((dot) => (\r\n        <motion.div\r\n          key={dot}\r\n          className=\"mx-0.5 h-1.5 w-1.5 rounded-full bg-primary\"\r\n          initial={{ opacity: 0.3 }}\r\n          animate={{\r\n            opacity: [0.3, 0.9, 0.3],\r\n            scale: [0.85, 1.1, 0.85],\r\n          }}\r\n          transition={{\r\n            duration: 1.2,\r\n            repeat: Infinity,\r\n            delay: dot * 0.15,\r\n            ease: \"easeInOut\",\r\n          }}\r\n          style={{\r\n            boxShadow: \"0 0 4px rgba(255, 255, 255, 0.3)\",\r\n          }}\r\n        />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\ninterface ActionButtonProps {\r\n  icon: React.ReactNode;\r\n  label: string;\r\n}\r\n\r\nconst rippleKeyframes = `\r\n@keyframes ripple {\r\n  0% { transform: scale(0.5); opacity: 0.6; }\r\n  100% { transform: scale(2); opacity: 0; }\r\n}\r\n`;\r\n\r\nif (typeof document !== \"undefined\") {\r\n  const style = document.createElement(\"style\");\r\n  style.innerHTML = rippleKeyframes;\r\n  document.head.appendChild(style);\r\n}\r\n", "path": "/components/mvpblocks/chatbot-ui/animated-ai-chat.tsx", "target": "/components/mvpblocks/animated-ai-chat.tsx"}]}