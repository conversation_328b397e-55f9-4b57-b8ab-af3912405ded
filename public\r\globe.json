{"name": "globe", "type": "registry:ui", "dependencies": ["lucide-react", "cobe"], "files": [{"type": "registry:ui", "content": "\"use client\";\r\n\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport createGlobe from \"cobe\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface EarthProps {\r\n  className?: string;\r\n  theta?: number;\r\n  dark?: number;\r\n  scale?: number;\r\n  diffuse?: number;\r\n  mapSamples?: number;\r\n  mapBrightness?: number;\r\n  baseColor?: [number, number, number];\r\n  markerColor?: [number, number, number];\r\n  glowColor?: [number, number, number];\r\n}\r\nconst Earth: React.FC<EarthProps> = ({\r\n  className,\r\n  theta = 0.25,\r\n  dark = 1,\r\n  scale = 1.1,\r\n  diffuse = 1.2,\r\n  mapSamples = 40000,\r\n  mapBrightness = 6,\r\n  baseColor = [0.4, 0.6509, 1],\r\n  markerColor = [1, 0, 0],\r\n  glowColor = [0.2745, 0.5765, 0.898],\r\n}) => {\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n\r\n  useEffect(() => {\r\n    let width = 0;\r\n    const onResize = () =>\r\n      canvasRef.current && (width = canvasRef.current.offsetWidth);\r\n    window.addEventListener(\"resize\", onResize);\r\n    onResize();\r\n    let phi = 0;\r\n\r\n    onResize();\r\n    const globe = createGlobe(canvasRef.current!, {\r\n      devicePixelRatio: 2,\r\n      width: width * 2,\r\n      height: width * 2,\r\n      phi: 0,\r\n      theta: theta,\r\n      dark: dark,\r\n      scale: scale,\r\n      diffuse: diffuse,\r\n      mapSamples: mapSamples,\r\n      mapBrightness: mapBrightness,\r\n      baseColor: baseColor,\r\n      markerColor: markerColor,\r\n      glowColor: glowColor,\r\n      opacity: 1,\r\n      offset: [0, 0],\r\n      markers: [\r\n        // longitude latitude\r\n      ],\r\n      onRender: (state: Record<string, any>) => {\r\n        // Called on every animation frame.\r\n        // `state` will be an empty object, return updated params.\\\r\n        state.phi = phi;\r\n        phi += 0.003;\r\n      },\r\n    });\r\n\r\n    return () => {\r\n      globe.destroy();\r\n    };\r\n  }, [dark]);\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"z-[10] mx-auto flex w-full max-w-[350px] items-center justify-center\",\r\n        className,\r\n      )}\r\n    >\r\n      <canvas\r\n        ref={canvasRef}\r\n        style={{\r\n          width: \"100%\",\r\n          height: \"100%\",\r\n          maxWidth: \"100%\",\r\n          aspectRatio: \"1\",\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Earth;\r\n", "path": "/components/ui/globe.tsx", "target": "/components/ui/globe.tsx"}]}