---
title: CTA
description: CTA sections are designed to encourage users to take action, whether it's signing up for a newsletter, downloading a resource, or making a purchase. They help drive conversions and improve user engagement.
root: mainsections
---

import {ComponentPreview} from "@/components/preview/component-preview";
import {extractSourceCode} from "@/lib/code";

## CTA 1

<ComponentPreview
  name="cta-1"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('cta-1')).code}
  lang="tsx"
  fromDocs={true}
/>

## CTA 2

<ComponentPreview
  name="cta-2"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('cta-2')).code}
  lang="tsx"
  fromDocs={true}
/>

## CTA 3

<ComponentPreview
  name="cta-3"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('cta-3')).code}
  lang="tsx"
  fromDocs={true}
/>