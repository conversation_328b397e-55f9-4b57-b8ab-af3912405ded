---
title: Hero
description: Hero sections are the first thing users see when they visit your site. They are a great way to showcase your product and its features.
root: mainsections
---

import { ComponentPreview } from "@/components/preview/component-preview";
import { extractSourceCode } from "@/lib/code";

## App Hero

<ComponentPreview
  name="app-hero"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode("app-hero")).code}
  lang="tsx"
  fromDocs={true}
/>

## Spline Hero

<ComponentPreview
  name="3dglobe"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode("3dglobe")).code}
  lang="tsx"
  fromDocs={true}
/>

> Apply the below CSS for the above hero:

```css
.neumorphic-button::after {
  content: '';
  @apply absolute inset-0 opacity-0 transition-opacity duration-300 bg-gradient-to-br from-[#9b87f5]/20 to-transparent rounded-full;
}
  
.neumorphic-button:hover::after {
  @apply opacity-100;
}
``` 

## Notebook Hero

<ComponentPreview
  name="notebook"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode("notebook")).code}
  lang="tsx"
  fromDocs={true}
/>

## Geometric Hero

<ComponentPreview
  name="geometric-hero"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode("geometric-hero")).code}
  lang="tsx"
  fromDocs={true}
/>

## Lucy Hero

<ComponentPreview
  name="mockup-hero"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode("mockup-hero")).code}
  lang="tsx"
  fromDocs={true}
/>

## Trading Hero

<ComponentPreview
  name="trading"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode("trading")).code}
  lang="tsx"
  fromDocs={true}
/>

## Dark modern hero

<ComponentPreview
  name="hero-1"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode("hero-1")).code}
  lang="tsx"
  fromDocs={true}
/>

## Fitness Hero

<ComponentPreview
  name="fitness-hero"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode("fitness-hero")).code}
  lang="tsx"
  fromDocs={true}
/>

## Gradient Hero

<ComponentPreview
  name="gradient-hero"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode("gradient-hero")).code}
  lang="tsx"
  fromDocs={true}
/>
