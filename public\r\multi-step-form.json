{"name": "multi-step-form", "type": "registry:ui", "dependencies": ["framer-motion", "lucide-react", "react", "react-hook-form", "zod"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/input.json", "https://blocks.mvp-subha.me/r/label.json", "https://blocks.mvp-subha.me/r/progress.json"], "files": [{"type": "registry:ui", "content": "\"use client\";\n\nimport React, { useState } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { z } from \"zod\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { cn } from \"@/lib/utils\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { CheckCircle2, ArrowRight, ArrowLeft } from \"lucide-react\";\n\n// Define the form schema for each step\nconst personalInfoSchema = z.object({\n  firstName: z.string().min(2, \"First name must be at least 2 characters\"),\n  lastName: z.string().min(2, \"Last name must be at least 2 characters\"),\n  email: z.string().email(\"Please enter a valid email address\"),\n});\n\nconst addressSchema = z.object({\n  address: z.string().min(5, \"Address must be at least 5 characters\"),\n  city: z.string().min(2, \"City must be at least 2 characters\"),\n  zipCode: z.string().min(5, \"Zip code must be at least 5 characters\"),\n});\n\nconst accountSchema = z.object({\n  username: z.string().min(3, \"Username must be at least 3 characters\"),\n  password: z\n    .string()\n    .min(8, \"Password must be at least 8 characters\")\n    .regex(/[A-Z]/, \"Password must contain at least one uppercase letter\")\n    .regex(/[0-9]/, \"Password must contain at least one number\"),\n  confirmPassword: z.string(),\n}).refine(data => data.password === data.confirmPassword, {\n  message: \"Passwords do not match\",\n  path: [\"confirmPassword\"],\n});\n\n// Combine all schemas for the final form data\nconst formSchema = z.object({\n  ...personalInfoSchema.shape,\n  ...addressSchema.shape,\n  ...accountSchema._def.schema.shape,\n});\n\ntype FormData = z.infer<typeof formSchema>;\n\ninterface MultiStepFormProps {\n  className?: string;\n  onSubmit?: (data: FormData) => void;\n}\n\nexport default function MultiStepForm({ className, onSubmit }: MultiStepFormProps) {\n  const [step, setStep] = useState(0);\n  const [formData, setFormData] = useState<Partial<FormData>>({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isComplete, setIsComplete] = useState(false);\n\n  // Define the steps\n  const steps = [\n    {\n      id: \"personal\",\n      title: \"Personal Information\",\n      description: \"Tell us about yourself\",\n      schema: personalInfoSchema,\n      fields: [\n        { name: \"firstName\", label: \"First Name\", type: \"text\", placeholder: \"John\" },\n        { name: \"lastName\", label: \"Last Name\", type: \"text\", placeholder: \"Doe\" },\n        { name: \"email\", label: \"Email\", type: \"email\", placeholder: \"<EMAIL>\" },\n      ],\n    },\n    {\n      id: \"address\",\n      title: \"Address Information\",\n      description: \"Where do you live?\",\n      schema: addressSchema,\n      fields: [\n        { name: \"address\", label: \"Address\", type: \"text\", placeholder: \"123 Main St\" },\n        { name: \"city\", label: \"City\", type: \"text\", placeholder: \"New York\" },\n        { name: \"zipCode\", label: \"Zip Code\", type: \"text\", placeholder: \"10001\" },\n      ],\n    },\n    {\n      id: \"account\",\n      title: \"Account Setup\",\n      description: \"Create your account\",\n      schema: accountSchema,\n      fields: [\n        { name: \"username\", label: \"Username\", type: \"text\", placeholder: \"johndoe\" },\n        { name: \"password\", label: \"Password\", type: \"password\", placeholder: \"••••••••\" },\n        { name: \"confirmPassword\", label: \"Confirm Password\", type: \"password\", placeholder: \"••••••••\" },\n      ],\n    },\n  ];\n\n  // Get the current step schema\n  const currentStepSchema = steps[step].schema;\n\n  // Setup form with the current step schema\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n  } = useForm<any>({\n    resolver: zodResolver(currentStepSchema as z.ZodType<any>),\n    defaultValues: formData,\n  });\n\n  // Calculate progress percentage\n  const progress = ((step + 1) / steps.length) * 100;\n\n  // Handle next step\n  const handleNextStep = (data: any) => {\n    const updatedData = { ...formData, ...data };\n    setFormData(updatedData);\n\n    if (step < steps.length - 1) {\n      setStep(step + 1);\n      // Reset form with the updated data for the next step\n      reset(updatedData);\n    } else {\n      // Final step submission\n      setIsSubmitting(true);\n      setTimeout(() => {\n        if (onSubmit) {\n          onSubmit(updatedData as FormData);\n        }\n        setIsComplete(true);\n        setIsSubmitting(false);\n      }, 1500);\n    }\n  };\n\n  // Handle previous step\n  const handlePrevStep = () => {\n    if (step > 0) {\n      setStep(step - 1);\n    }\n  };\n\n  // Animation variants\n  const variants = {\n    hidden: { opacity: 0, x: 50 },\n    visible: { opacity: 1, x: 0 },\n    exit: { opacity: 0, x: -50 },\n  };\n\n  return (\n    <div className={cn(\"w-full max-w-md mx-auto p-6 rounded-lg shadow-lg bg-card/40\", className)}>\n      {!isComplete ? (\n        <>\n          {/* Progress bar */}\n          <div className=\"mb-8\">\n            <div className=\"flex justify-between mb-2\">\n              <span className=\"text-sm font-medium\">Step {step + 1} of {steps.length}</span>\n              <span className=\"text-sm font-medium\">{Math.round(progress)}%</span>\n            </div>\n            <Progress value={progress} className=\"h-2\" />\n          </div>\n\n          {/* Step indicators */}\n          <div className=\"flex justify-between mb-8\">\n            {steps.map((s, i) => (\n              <div key={s.id} className=\"flex flex-col items-center\">\n                <div\n                  className={cn(\n                    \"w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold\",\n                    i < step\n                      ? \"bg-primary text-primary-foreground\"\n                      : i === step\n                      ? \"bg-primary text-primary-foreground ring-2 ring-primary/30\"\n                      : \"bg-secondary text-secondary-foreground\"\n                  )}\n                >\n                  {i < step ? <CheckCircle2 className=\"h-4 w-4\" /> : i + 1}\n                </div>\n                <span className=\"text-xs mt-1 hidden sm:block\">{s.title}</span>\n              </div>\n            ))}\n          </div>\n\n          {/* Form */}\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={step}\n              initial=\"hidden\"\n              animate=\"visible\"\n              exit=\"exit\"\n              variants={variants}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"mb-6\">\n                <h2 className=\"text-xl font-bold\">{steps[step].title}</h2>\n                <p className=\"text-sm text-muted-foreground\">{steps[step].description}</p>\n              </div>\n\n              <form onSubmit={handleSubmit(handleNextStep)} className=\"space-y-4\">\n                {steps[step].fields.map((field) => (\n                  <div key={field.name} className=\"space-y-2\">\n                    <Label htmlFor={field.name}>{field.label}</Label>\n                    <Input\n                      id={field.name}\n                      type={field.type}\n                      placeholder={field.placeholder}\n                      {...register(field.name as any)}\n                      className={cn(errors[field.name as string] && \"border-destructive\")}\n                    />\n                    {errors[field.name as string] && (\n                      <p className=\"text-sm text-destructive\">\n                        {errors[field.name as string]?.message as string}\n                      </p>\n                    )}\n                  </div>\n                ))}\n\n                <div className=\"flex justify-between pt-4\">\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    onClick={handlePrevStep}\n                    disabled={step === 0}\n                    className={cn(step === 0 && \"invisible\")}\n                  >\n                    <ArrowLeft className=\"mr-2 h-4 w-4\" /> Back\n                  </Button>\n                  <Button type=\"submit\" disabled={isSubmitting}>\n                    {step === steps.length - 1 ? (\n                      isSubmitting ? \"Submitting...\" : \"Submit\"\n                    ) : (\n                      <>\n                        Next <ArrowRight className=\"ml-2 h-4 w-4\" />\n                      </>\n                    )}\n                  </Button>\n                </div>\n              </form>\n            </motion.div>\n          </AnimatePresence>\n        </>\n      ) : (\n        <motion.div\n          initial={{ opacity: 0, scale: 0.9 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.5 }}\n          className=\"text-center py-10\"\n        >\n          <div className=\"inline-flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 mb-4\">\n            <CheckCircle2 className=\"h-8 w-8 text-primary\" />\n          </div>\n          <h2 className=\"text-2xl font-bold mb-2\">Form Submitted!</h2>\n          <p className=\"text-muted-foreground mb-6\">\n            Thank you for completing the form. We&apos;ll be in touch soon.\n          </p>\n          <Button onClick={() => {\n            setStep(0);\n            setFormData({});\n            setIsComplete(false);\n            reset({});\n          }}>\n            Start Over\n          </Button>\n        </motion.div>\n      )}\n    </div>\n  );\n}\n", "path": "/components/ui/multi-step-form.tsx", "target": "/components/ui/multi-step-form.tsx"}]}