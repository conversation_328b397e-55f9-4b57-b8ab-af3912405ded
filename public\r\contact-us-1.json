{"name": "contact-us-1", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/globe.json", "https://blocks.mvp-subha.me/r/input.json", "https://blocks.mvp-subha.me/r/label.json", "https://blocks.mvp-subha.me/r/sparkles.json", "https://blocks.mvp-subha.me/r/textarea.json"], "files": [{"type": "registry:block", "content": "\"use client\";\n\nimport { useState, useRef } from \"react\";\nimport { motion, useInView } from \"framer-motion\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Button } from \"@/components/ui/button\";\nimport Earth from \"@/components/ui/globe\";\nimport { SparklesCore } from \"@/components/ui/sparkles\";\nimport { Label } from \"@/components/ui/label\";\nimport { Check, Loader2 } from \"lucide-react\";\n\nexport default function ContactUs1() {\n  const [name, setName] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [message, setMessage] = useState(\"\");\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n\n  const formRef = useRef(null);\n  const isInView = useInView(formRef, { once: true, amount: 0.3 });\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n\n    try {\n      // Perform form submission logic here\n      console.log(\"Form submitted:\", { name, email, message });\n      await new Promise((resolve) => setTimeout(resolve, 1000));\n      setName(\"\");\n      setEmail(\"\");\n      setMessage(\"\");\n      setIsSubmitted(true);\n      setTimeout(() => {\n        setIsSubmitted(false);\n      }, 5000);\n    } catch (error) {\n      console.error(\"Error submitting form:\", error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <section className=\"relative w-full overflow-hidden bg-background py-16 md:py-24\">\n      <div\n        className=\"absolute left-0 top-0 h-[500px] w-[500px] rounded-full opacity-20 blur-[120px]\"\n        style={{\n          background: `radial-gradient(circle at center, #e60a64, transparent 70%)`,\n        }}\n      />\n      <div\n        className=\"absolute bottom-0 right-0 h-[300px] w-[300px] rounded-full opacity-10 blur-[100px]\"\n        style={{\n          background: `radial-gradient(circle at center, #e60a64, transparent 70%)`,\n        }}\n      />\n\n      <div className=\"container relative z-10 mx-auto px-4 md:px-6\">\n        <div className=\"mx-auto max-w-5xl overflow-hidden rounded-[28px] border border-border/40 bg-secondary/20 shadow-xl backdrop-blur-sm\">\n          <div className=\"grid md:grid-cols-2\">\n            <div className=\"relative p-6 md:p-10\" ref={formRef}>\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={\n                  isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }\n                }\n                transition={{ duration: 0.5, delay: 0.1 }}\n                className=\"flex w-full gap-2\"\n              >\n                <h2 className=\"mb-2 bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-4xl font-bold tracking-tight text-transparent md:text-5xl\">\n                  Contact\n                </h2>\n                <span className=\"relative z-10 w-full text-4xl font-bold italic tracking-tight text-primary md:text-5xl\">\n                  Us\n                </span>\n                <SparklesCore\n                  id=\"tsparticles\"\n                  background=\"transparent\"\n                  minSize={0.6}\n                  maxSize={1.4}\n                  particleDensity={500}\n                  className=\"absolute inset-0 top-0 h-24 w-full\"\n                  particleColor=\"#e60a64\"\n                />\n              </motion.div>\n\n              <motion.form\n                initial={{ opacity: 0, y: 20 }}\n                animate={\n                  isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }\n                }\n                transition={{ duration: 0.5, delay: 0.3 }}\n                onSubmit={handleSubmit}\n                className=\"mt-8 space-y-6\"\n              >\n                <div className=\"grid grid-cols-1 gap-6 md:grid-cols-2\">\n                  <motion.div\n                    className=\"space-y-2\"\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.4 }}\n                  >\n                    <Label htmlFor=\"name\">Name</Label>\n                    <Input\n                      id=\"name\"\n                      value={name}\n                      onChange={(e) => setName(e.target.value)}\n                      placeholder=\"Enter your name\"\n                      required\n                    />\n                  </motion.div>\n\n                  <motion.div\n                    className=\"space-y-2\"\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.5 }}\n                  >\n                    <Label htmlFor=\"email\">Email</Label>\n                    <Input\n                      id=\"email\"\n                      type=\"email\"\n                      value={email}\n                      onChange={(e) => setEmail(e.target.value)}\n                      placeholder=\"Enter your email\"\n                      required\n                    />\n                  </motion.div>\n                </div>\n\n                <motion.div\n                  className=\"space-y-2\"\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.6 }}\n                >\n                  <Label htmlFor=\"message\">Message</Label>\n                  <Textarea\n                    id=\"message\"\n                    value={message}\n                    onChange={(e) => setMessage(e.target.value)}\n                    placeholder=\"Enter your message\"\n                    required\n                    className=\"h-40 resize-none\"\n                  />\n                </motion.div>\n\n                <motion.div\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"w-full\"\n                >\n                  <Button\n                    type=\"submit\"\n                    disabled={isSubmitting}\n                    className=\"w-full bg-gradient-to-b from-rose-500 to-rose-700 text-white shadow-[0px_2px_0px_0px_rgba(255,255,255,0.3)_inset]\"\n                  >\n                    {isSubmitting ? (\n                      <span className=\"flex items-center justify-center\">\n                        <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                        Sending...\n                      </span>\n                    ) : isSubmitted ? (\n                      <span className=\"flex items-center justify-center\">\n                        <Check className=\"mr-2 h-4 w-4\" />\n                        Message Sent!\n                      </span>\n                    ) : (\n                      <span>Send Message</span>\n                    )}\n                  </Button>\n                </motion.div>\n              </motion.form>\n            </div>\n\n            <motion.div\n              initial={{ opacity: 0, x: 20 }}\n              animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 20 }}\n              transition={{ duration: 0.5, delay: 0.5 }}\n              className=\"relative my-8 flex items-center justify-center overflow-hidden pr-8\"\n            >\n              <div className=\"flex flex-col items-center justify-center overflow-hidden\">\n                <article className=\"relative mx-auto h-[350px] min-h-60 max-w-[450px] overflow-hidden rounded-3xl border bg-gradient-to-b from-[#e60a64] to-[#e60a64]/5 p-6 text-3xl tracking-tight text-white md:h-[450px] md:min-h-80 md:p-8 md:text-4xl md:leading-[1.05] lg:text-5xl\">\n                  Presenting you with the best UI possible.\n                  <div className=\"absolute -bottom-20 -right-20 z-10 mx-auto flex h-full w-full max-w-[300px] items-center justify-center transition-all duration-700 hover:scale-105 md:-bottom-28 md:-right-28 md:max-w-[550px]\">\n                    <Earth\n                      scale={1.1}\n                      baseColor={[1, 0, 0.3]}\n                      markerColor={[0, 0, 0]}\n                      glowColor={[1, 0.3, 0.4]}\n                    />\n                  </div>\n                </article>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n", "path": "/components/mvpblocks/mainsections/contact/contact-us-1.tsx", "target": "/components/mvpblocks/contact-us-1.tsx"}]}