---
title: Typewriter
description: Typewriter animations simulate the effect of text being typed out in real-time. This technique can be used to create a sense of anticipation and engagement as users watch the text appear on the screen.
root: text-animations
---

import { ComponentPreview } from "@/components/preview/component-preview";
import { extractSourceCode } from "@/lib/code";
import { ComponentSource } from "@/components/preview/component-source";
import { Tab, Tabs } from "fumadocs-ui/components/tabs";
import { Step, Steps } from "fumadocs-ui/components/steps";
import { TypeTable } from "fumadocs-ui/components/type-table";

<ComponentPreview
  name="typewriter-demo"
  classNameComponentContainer="h-[300px]"
  code={(await extractSourceCode("typewriter-demo")).code}
  lang="tsx"
  hasReTrigger={true}
/>

## Installation

<Steps>
  <Step>
    <Tabs items={["npm", "pnpm", "yarn", "bun"]}>
      <Tab>
        ```bash
        npm install framer-motion
        ```
      </Tab>
      <Tab>
      ```bash 
      pnpm install framer-motion 
      ```
      </Tab>
      <Tab>
      ```bash 
      yarn add framer-motion 
      ```
      </Tab>
      <Tab>
      ```bash 
      bun add framer-motion 
      ```
      </Tab>
    </Tabs>
  </Step>

  <Step>
    #### Copy and paste the following code into your project.

    `components/ui/typewriter.tsx`

    ```tsx title="typewriter.tsx"
    "use client";

    import { useEffect } from "react";
    import { motion, useMotionValue, useTransform, animate } from "framer-motion";

    export default function TextGenerateEffect({
      words,
      className = "",
    }: {
      words: string;
      className?: string;
    }) {
      const count = useMotionValue(0);
      const rounded = useTransform(count, (latest) => Math.round(latest));
      const displayText = useTransform(rounded, (latest) =>
        words.slice(0, latest)
      );

      useEffect(() => {
        const controls = animate(count, words.length, {
          type: "tween",
          duration: 2.5, // Increased from 1 to 2.5 seconds
          ease: "easeInOut",
        });
        return controls.stop;
      }, [words]);

      return (
        <motion.span className={className}>
          {displayText}
        </motion.span>
      );
  };
  ```

  </Step>
</Steps>

## Props

<TypeTable
  type={{
    words: {
      description: "The text to be typed out.",
      type: "string",
      default: "Hello World!",
    },
    className: {
      description:
        "Optional CSS class for customizing the style and appearance of the text.",
      type: "string",
      default: undefined,
    },
  }}
/>

## Gradient Typewriter

<ComponentPreview
  name="gradient-typewriter"
  classNameComponentContainer="h-[300px]"
  code={(await extractSourceCode("gradient-typewriter")).code}
  lang="tsx"
  hasReTrigger={true}
/>
