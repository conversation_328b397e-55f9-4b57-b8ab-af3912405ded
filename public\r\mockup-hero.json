{"name": "mockup-hero", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "next-themes", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/phone-mockup.json"], "files": [{"type": "registry:block", "content": "\"use client\";\n\nimport React, { useRef, useEffect, useState } from \"react\";\nimport { motion, useAnimation, useInView, useScroll, useTransform, useMotionValue } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport PhoneMockup from \"@/components/ui/phone-mockup\";\nimport { useTheme } from \"next-themes\";\nimport { ArrowR<PERSON>, <PERSON>rkles } from \"lucide-react\";\n\nexport default function LucyHero() {\n  const { theme } = useTheme();\n  const isDark = theme === \"dark\";\n  const heroRef = useRef<HTMLDivElement>(null);\n  const mockupRef = useRef<HTMLDivElement>(null);\n  const isInView = useInView(heroRef, { once: false, amount: 0.3 });\n  const controls = useAnimation();\n  const { scrollYProgress } = useScroll({\n    target: heroRef,\n    offset: [\"start start\", \"end start\"],\n  });\n\n  const backgroundY = useTransform(scrollYProgress, [0, 1], [0, 100]);\n  const contentY = useTransform(scrollYProgress, [0, 1], [0, 50]);\n\n\n  const [isHovered, setIsHovered] = useState(false);\n  const mouseX = useMotionValue(0);\n  const mouseY = useMotionValue(0);\n\n  const rotateX = useTransform(mouseY, [-0.5, 0, 0.5], [20, 0, -20]);\n  const rotateY = useTransform(mouseX, [-0.5, 0, 0.5], [-20, 0, 20]);\n\n  useEffect(() => {\n    if (isInView) {\n      controls.start(\"visible\");\n    }\n  }, [isInView, controls]);\n\n  const GradientText = ({ children, className }: { children: React.ReactNode; className?: string }) => (\n    <span\n      className={cn(\n        \"bg-gradient-to-r dark:from-primary dark:via-rose-300 dark:to-red-400 from-primary via-rose-400 to-rose-300 bg-clip-text text-transparent\",\n        className\n      )}\n    >\n      {children}\n    </span>\n  );\n\n  return (\n    <div\n      ref={heroRef}\n      className=\"relative min-h-screen w-full overflow-hidden bg-background py-16\"\n    >\n      <motion.div\n        className=\"absolute inset-0 z-0\"\n        style={{ y: backgroundY }}\n      >\n        <div className=\"absolute inset-0 dark:bg-[radial-gradient(ellipse_80%_80%_at_50%_-20%,rgba(229,62,62,0.15),rgba(30,30,40,0))] bg-[radial-gradient(ellipse_80%_80%_at_50%_-20%,rgba(229,62,62,0.2),rgba(255,255,255,0))]\"></div>\n\n        <div className=\"absolute inset-0 dark:bg-[radial-gradient(circle_at_10%_90%,rgba(120,119,198,0.1),transparent_50%)] bg-[radial-gradient(circle_at_10%_90%,rgba(229,62,62,0.08),transparent_50%)]\"></div>\n        <div className=\"absolute inset-0 dark:bg-[radial-gradient(circle_at_90%_20%,rgba(100,150,255,0.05),transparent_50%)] bg-[radial-gradient(circle_at_90%_20%,rgba(255,100,150,0.05),transparent_50%)]\"></div>\n\n        <div className=\"absolute inset-0 bg-noise opacity-[0.02]\"></div>\n        <div className=\"absolute inset-0 opacity-5 backdrop-blur-[100px]\"></div>\n        <div className=\"absolute inset-0 dark:opacity-[0.02] opacity-[0.03] dark:[background-image:linear-gradient(rgba(200,200,255,0.05)_1px,transparent_1px),linear-gradient(to_right,rgba(200,200,255,0.05)_1px,transparent_1px)] [background-image:linear-gradient(rgba(229,62,62,0.05)_1px,transparent_1px),linear-gradient(to_right,rgba(229,62,62,0.05)_1px,transparent_1px)] [background-size:40px_40px]\"></div>\n      </motion.div>\n\n      <motion.div\n        className=\"container relative z-10 mx-auto max-w-7xl\"\n        style={{ y: contentY }}\n      >\n        <div className=\"grid items-center gap-16 md:grid-cols-2\">\n          <motion.div\n            variants={{\n              hidden: { opacity: 0, x: -50 },\n              visible: {\n                opacity: 1,\n                x: 0,\n                transition: {\n                  duration: 0.7,\n                  staggerChildren: 0.2\n                }\n              }\n            }}\n            initial=\"hidden\"\n            animate={controls}\n            className=\"flex flex-col md:text-left text-center\"\n          >\n            <motion.div variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}>\n              <h2 className=\n                \"mb-6 text-4xl font-bold leading-tight tracking-tight text-foreground md:text-5xl lg:text-6xl\">\n                LU-cy bridges <GradientText>Web3</GradientText> and <GradientText>AI</GradientText> platforms for dev teams\n              </h2>\n            </motion.div>\n\n            <motion.p\n              variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}\n              className=\"mb-8 text-lg text-muted-foreground leading-relaxed\"\n            >\n              The future is a blend of intelligence and decentralization. LU-cy connects AI tools with Web3 infrastructure, giving developers the power to build beyond limits. One platform. <span className=\"font-semibold text-foreground\">Endless potential.</span>\n            </motion.p>\n\n            <motion.div\n              variants={{ hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0 } }}\n              className=\"flex flex-wrap gap-4 justify-center md:justify-start\"\n            >\n              <motion.div\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"relative\"\n              >\n                <Button\n                  className=\"relative rounded-full\"\n                >\n                  Explore\n                  <Sparkles className=\"h-4 w-4\" />\n                </Button>\n              </motion.div>\n\n              <motion.div\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"relative\"\n              >\n                <div className=\"absolute inset-0 rounded-full bg-background/50 backdrop-blur-sm -z-10\"></div>\n                <Button\n                  variant=\"outline\"\n                  className=\"rounded-full border-primary/20 hover:bg-primary/5 hover:border-primary/30 transition-all duration-300 backdrop-blur-sm\"\n                >\n                  Learn More <ArrowRight className=\"ml-2 h-4 w-4\" />\n                </Button>\n              </motion.div>\n            </motion.div>\n\n            <motion.div\n              variants={{ hidden: { opacity: 0 }, visible: { opacity: 1 } }}\n              className=\"mt-10 flex flex-wrap gap-3 justify-center md:justify-start\"\n            >\n              {[\"Web3 Ready\", \"AI Powered\", \"Developer First\"].map((feature, index) => (\n                <motion.div\n                  key={feature}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.6 + index * 0.1 }}\n                  whileHover={{ scale: 1.05, y: -2 }}\n                  className=\"relative rounded-full px-4 py-1.5 text-sm font-medium text-foreground shadow-sm\"\n                >\n                  <div className=\"absolute inset-0 rounded-full dark:bg-background/30 bg-background/80 backdrop-blur-md border dark:border-white/5 border-primary/10\"></div>\n                  <div className=\"absolute bottom-0 left-1/2 h-px w-1/2 -translate-x-1/2 bg-gradient-to-r dark:from-blue-500/0 dark:via-primary/30 dark:to-indigo-500/0 from-rose-500/0 via-primary/20 to-rose-500/0\"></div>\n\n                  <span className=\"relative z-10\">{feature}</span>\n                </motion.div>\n              ))}\n            </motion.div>\n          </motion.div>\n\n          <motion.div\n            variants={{\n              hidden: { opacity: 0, scale: 0.9 },\n              visible: {\n                opacity: 1,\n                scale: 1,\n                transition: {\n                  duration: 0.8,\n                  type: \"spring\",\n                  stiffness: 100\n                }\n              }\n            }}\n            initial=\"hidden\"\n            animate={controls}\n            ref={mockupRef}\n            className=\"relative mx-auto flex justify-center\"\n            style={{\n              transformStyle: \"preserve-3d\",\n              perspective: \"1000px\"\n            }}\n            onMouseMove={(e) => {\n              const rect = e.currentTarget.getBoundingClientRect();\n              const x = (e.clientX - rect.left) / rect.width - 0.5;\n              const y = (e.clientY - rect.top) / rect.height - 0.5;\n              mouseX.set(x);\n              mouseY.set(y);\n\n              if (!isHovered) {\n                setIsHovered(true);\n              }\n            }}\n            onMouseLeave={() => {\n              mouseX.set(0);\n              mouseY.set(0);\n              setIsHovered(false);\n            }}\n          >\n            <motion.div\n              className=\"z-10 relative\"\n              style={{\n                transformStyle: \"preserve-3d\",\n                rotateX: rotateX,\n                rotateY: rotateY,\n                scale: isHovered ? 1.05 : 1,\n                transition: \"scale 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)\"\n              }}\n            >\n              <PhoneMockup\n                imageUrl={isDark ? \"https://blocks.mvp-subha.me/mobile-dark.webp\" : \"https://blocks.mvp-subha.me/mobile-light.webp\"}\n                alt=\"LU-cy mobile app\"\n                glowColor={isDark ? \"rgba(229, 62, 62, 0.5)\" : \"rgba(229, 62, 62, 0.25)\"}\n                className=\"max-w-[380px]\"\n              />\n            </motion.div>\n          </motion.div>\n        </div>\n      </motion.div>\n    </div>\n  );\n}\n\n\n", "path": "/components/mvpblocks/mainsections/hero/mockup-hero.tsx", "target": "/components/mvpblocks/mockup-hero.tsx"}]}