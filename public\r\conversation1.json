{"name": "conversation1", "type": "registry:block", "dependencies": ["lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/input.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "\"use client\"\r\n\r\nimport { useState } from \"react\"\r\nimport { Send, Mic, User, <PERSON><PERSON> } from \"lucide-react\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst initialMessages = [\r\n  {\r\n    id: \"1\",\r\n    content: \"Hi there! Welcome to our new platform. How can I assist you today?\",\r\n    sender: \"ai\",\r\n    timestamp: new Date(Date.now() - 1000 * 60 * 10).toISOString(),\r\n  },\r\n  {\r\n    id: \"2\",\r\n    content: \"Hello! I'm looking for information about your premium subscription plans.\",\r\n    sender: \"user\",\r\n    timestamp: new Date(Date.now() - 1000 * 60 * 9).toISOString(),\r\n  },\r\n  {\r\n    id: \"3\",\r\n    content:\r\n      \"Great question! Our premium plan offers unlimited access to all features, priority support, and advanced analytics. It's priced at $19.99/month with a 7-day free trial.\",\r\n    sender: \"ai\",\r\n    timestamp: new Date(Date.now() - 1000 * 60 * 8).toISOString(),\r\n  },\r\n  {\r\n    id: \"4\",\r\n    content: \"That sounds interesting. Are there any discounts for annual subscriptions?\",\r\n    sender: \"user\",\r\n    timestamp: new Date(Date.now() - 1000 * 60 * 7).toISOString(),\r\n  },\r\n  {\r\n    id: \"5\",\r\n    content:\r\n      \"We offer a 20% discount on annual subscriptions, bringing the price down to $191.90 per year, which saves you about $48 compared to the monthly plan.\",\r\n    sender: \"ai\",\r\n    timestamp: new Date(Date.now() - 1000 * 60 * 6).toISOString(),\r\n  },\r\n]\r\n\r\ninterface ChatBubbleProps {\r\n  message: string\r\n  isUser: boolean\r\n  timestamp: Date\r\n}\r\n\r\nfunction ChatBubble({ message, isUser, timestamp }: ChatBubbleProps) {\r\n  const formattedTime = timestamp.toLocaleTimeString([], {\r\n    hour: \"2-digit\",\r\n    minute: \"2-digit\",\r\n    hour12: true,\r\n  })\r\n\r\n  return (\r\n    <div className={cn(\"flex w-full\", isUser ? \"justify-end\" : \"justify-start\")}>\r\n      <div className={cn(\"flex items-start max-w-[80%] space-x-2\", isUser && \"flex-row-reverse space-x-reverse\")}>\r\n        <div\r\n          className={cn(\r\n            \"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center\",\r\n            isUser ? \"bg-primary/10\" : \"bg-muted\",\r\n          )}\r\n        >\r\n          {isUser ? <User className=\"h-4 w-4 text-primary\" /> : <Bot className=\"h-4 w-4 text-muted-foreground\" />}\r\n        </div>\r\n\r\n        <div className=\"flex flex-col\">\r\n          <div\r\n            className={cn(\r\n              \"rounded-2xl px-4 py-2 shadow-sm\",\r\n              isUser\r\n                ? \"bg-primary text-primary-foreground rounded-tr-none\"\r\n                : \"bg-card border border-border text-card-foreground rounded-tl-none\",\r\n            )}\r\n          >\r\n            <p className=\"whitespace-pre-wrap\">{message}</p>\r\n          </div>\r\n\r\n          <span className={cn(\"text-xs mt-1 text-muted-foreground\", isUser ? \"text-right\" : \"text-left\")}>{formattedTime}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default function Conversation1() {\r\n  const [messages, setMessages] = useState(initialMessages)\r\n  const [input, setInput] = useState(\"\")\r\n  const [isTyping, setIsTyping] = useState(false)\r\n\r\n  const handleSendMessage = (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n    if (!input.trim()) return\r\n    const userMessage = {\r\n      id: Date.now().toString(),\r\n      content: input,\r\n      sender: \"user\",\r\n      timestamp: new Date().toISOString(),\r\n    }\r\n\r\n    setMessages((prev) => [...prev, userMessage])\r\n    setInput(\"\")\r\n    setIsTyping(true)\r\n    setTimeout(() => {\r\n      const aiMessage = {\r\n        id: (Date.now() + 1).toString(),\r\n        content:\r\n          \"Thanks for your message! I'm here to help with any other questions you might have about our services or features.\",\r\n        sender: \"ai\",\r\n        timestamp: new Date().toISOString(),\r\n      }\r\n\r\n      setMessages((prev) => [...prev, aiMessage])\r\n      setIsTyping(false)\r\n    }, 1500)\r\n  }\r\n\r\n  return (\r\n    <main className=\"flex min-h-screen flex-col items-center justify-center p-4 bg-background\">\r\n      <div className=\"w-full max-w-2xl bg-card rounded-xl shadow-lg overflow-hidden border border-border\">\r\n        <div className=\"bg-primary p-4\">\r\n          <h1 className=\"text-primary-foreground font-semibold text-lg\">AI Assistant</h1>\r\n          <p className=\"text-primary-foreground/80 text-sm\">Always here to help you</p>\r\n        </div>\r\n\r\n        <div className=\"flex flex-col h-[600px]\">\r\n          <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\r\n            {messages.map((message) => (\r\n              <ChatBubble\r\n                key={message.id}\r\n                message={message.content}\r\n                isUser={message.sender === \"user\"}\r\n                timestamp={new Date(message.timestamp)}\r\n              />\r\n            ))}\r\n\r\n            {isTyping && (\r\n              <div className=\"flex items-center space-x-2 text-muted-foreground text-sm\">\r\n                <div className=\"flex space-x-1\">\r\n                  <div\r\n                    className=\"w-2 h-2 bg-muted-foreground/70 rounded-full animate-bounce\"\r\n                    style={{ animationDelay: \"0ms\" }}\r\n                  ></div>\r\n                  <div\r\n                    className=\"w-2 h-2 bg-muted-foreground/70 rounded-full animate-bounce\"\r\n                    style={{ animationDelay: \"150ms\" }}\r\n                  ></div>\r\n                  <div\r\n                    className=\"w-2 h-2 bg-muted-foreground/70 rounded-full animate-bounce\"\r\n                    style={{ animationDelay: \"300ms\" }}\r\n                  ></div>\r\n                </div>\r\n                <span>AI is typing...</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"border-t border-border p-4\">\r\n            <form onSubmit={handleSendMessage} className=\"flex space-x-2\">\r\n              <Input\r\n                value={input}\r\n                onChange={(e) => setInput(e.target.value)}\r\n                placeholder=\"Type your message...\"\r\n                className=\"flex-1\"\r\n              />\r\n              <Button type=\"submit\" size=\"icon\" className=\"bg-primary hover:bg-primary/90\">\r\n                <Send className=\"h-4 w-4\" />\r\n              </Button>\r\n              <Button type=\"button\" size=\"icon\" variant=\"outline\">\r\n                <Mic className=\"h-4 w-4\" />\r\n              </Button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </main>\r\n  )\r\n}\r\n", "path": "/components/mvpblocks/chatbot-ui/conversation1.tsx", "target": "/components/mvpblocks/conversation1.tsx"}]}