{"name": "gradient-hero", "author": "Xeven777", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { <PERSON><PERSON><PERSON>, ChevronRight, ExternalLink, Github } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport default function GradientHero() {\r\n  return (\r\n    <div className=\"relative w-full overflow-hidden bg-background\">\r\n      {/* Background gradient */}\r\n      <div className=\"absolute inset-0 z-0\">\r\n        <div className=\"absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-primary/20 via-background to-background\"></div>\r\n        <div className=\"absolute left-1/2 top-0 -z-10 h-[1000px] w-[1000px] -translate-x-1/2 rounded-full bg-primary/5 blur-3xl\"></div>\r\n      </div>\r\n      <div className=\"absolute inset-0 bg-[linear-gradient(to_right,#8882_1px,transparent_1px),linear-gradient(to_bottom,#8882_1px,transparent_1px)] bg-[size:16px_16px] opacity-15\"></div>\r\n\r\n      <div className=\"container relative z-10 mx-auto px-4 py-24 sm:px-6 lg:px-8 lg:py-32\">\r\n        <div className=\"mx-auto max-w-5xl\">\r\n          {/* Badge */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5 }}\r\n            className=\"mx-auto mb-6 flex justify-center\"\r\n          >\r\n            <div className=\"inline-flex items-center rounded-full border border-border bg-background/80 px-3 py-1 text-sm backdrop-blur-sm\">\r\n              <span className=\"mr-2 rounded-full bg-primary px-2 py-0.5 text-xs font-semibold text-white\">\r\n                New\r\n              </span>\r\n              <span className=\"text-muted-foreground\">\r\n                Introducing our latest component library\r\n              </span>\r\n              <ChevronRight className=\"ml-1 h-4 w-4 text-muted-foreground\" />\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Heading */}\r\n          <motion.h1\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5, delay: 0.1 }}\r\n            className=\"text-balance bg-gradient-to-tl from-primary/10 via-foreground/85 to-foreground/50 bg-clip-text text-center text-4xl tracking-tighter text-transparent sm:text-5xl md:text-6xl lg:text-7xl\"\r\n          >\r\n            Build beautiful interfaces with speed and precision\r\n          </motion.h1>\r\n\r\n          {/* Description */}\r\n          <motion.p\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5, delay: 0.2 }}\r\n            className=\"mx-auto mt-6 max-w-2xl text-center text-lg text-muted-foreground\"\r\n          >\r\n            A modern UI component library designed to help developers create\r\n            stunning web applications with minimal effort. Fully customizable,\r\n            responsive, and accessible.\r\n          </motion.p>\r\n\r\n          {/* CTA Buttons */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5, delay: 0.3 }}\r\n            className=\"mt-10 flex flex-col items-center justify-center gap-4 sm:flex-row\"\r\n          >\r\n            <Button\r\n              size=\"lg\"\r\n              className=\"group relative overflow-hidden rounded-full bg-primary px-6 text-primary-foreground shadow-lg transition-all duration-300 hover:shadow-primary/30\"\r\n            >\r\n              <span className=\"relative z-10 flex items-center\">\r\n                Get Started\r\n                <ArrowRight className=\"ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1\" />\r\n              </span>\r\n              <span className=\"absolute inset-0 z-0 bg-gradient-to-r from-primary via-primary/90 to-primary/80 opacity-0 transition-opacity duration-300 group-hover:opacity-100\"></span>\r\n            </Button>\r\n\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"lg\"\r\n              className=\"flex items-center gap-2 rounded-full border-border bg-background/50 backdrop-blur-sm\"\r\n            >\r\n              <Github className=\"h-4 w-4\" />\r\n              Star on GitHub\r\n            </Button>\r\n          </motion.div>\r\n\r\n          {/* Feature Image */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 40 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{\r\n              duration: 0.8,\r\n              delay: 0.5,\r\n              type: \"spring\",\r\n              stiffness: 50,\r\n            }}\r\n            className=\"relative mx-auto mt-16 max-w-4xl\"\r\n          >\r\n            <div className=\"overflow-hidden rounded-xl border border-border/40 bg-background/50 shadow-xl backdrop-blur-sm\">\r\n              <div className=\"flex h-10 items-center border-b border-border/40 bg-muted/50 px-4\">\r\n                <div className=\"flex space-x-2\">\r\n                  <div className=\"h-3 w-3 rounded-full bg-red-500\"></div>\r\n                  <div className=\"h-3 w-3 rounded-full bg-yellow-500\"></div>\r\n                  <div className=\"h-3 w-3 rounded-full bg-green-500\"></div>\r\n                </div>\r\n                <div className=\"mx-auto flex items-center rounded-md bg-background/50 px-3 py-1 text-xs text-muted-foreground\">\r\n                  https://your-awesome-app.com\r\n                </div>\r\n              </div>\r\n              <div className=\"relative\">\r\n                <img\r\n                  src=\"https://i.postimg.cc/0yk8Vz7t/dashboard.webp\"\r\n                  alt=\"Dashboard Preview\"\r\n                  className=\"w-full\"\r\n                />\r\n                <div className=\"absolute inset-0 bg-gradient-to-t from-background to-transparent opacity-0\"></div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Floating elements for visual interest */}\r\n            <div className=\"absolute -right-6 -top-6 h-12 w-12 rounded-lg border border-border/40 bg-background/80 p-3 shadow-lg backdrop-blur-md\">\r\n              <div className=\"h-full w-full rounded-md bg-primary/20\"></div>\r\n            </div>\r\n            <div className=\"absolute -bottom-4 -left-4 h-8 w-8 rounded-full border border-border/40 bg-background/80 shadow-lg backdrop-blur-md\"></div>\r\n            <div className=\"absolute -bottom-6 right-12 h-10 w-10 rounded-lg border border-border/40 bg-background/80 p-2 shadow-lg backdrop-blur-md\">\r\n              <div className=\"h-full w-full rounded-md bg-green-500/20\"></div>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/hero/gradient-hero.tsx", "target": "/components/mvpblocks/gradient-hero.tsx"}]}