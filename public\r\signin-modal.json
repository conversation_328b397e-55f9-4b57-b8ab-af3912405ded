{"name": "signin-modal", "type": "registry:block", "dependencies": ["react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/checkbox.json", "https://blocks.mvp-subha.me/r/dialog.json", "https://blocks.mvp-subha.me/r/input.json", "https://blocks.mvp-subha.me/r/label.json"], "files": [{"type": "registry:block", "content": "import { useId } from \"react\"\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { Checkbox } from \"@/components/ui/checkbox\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from \"@/components/ui/dialog\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nexport default function SigninModal() {\r\n  const id = useId()\r\n  return (\r\n    <Dialog>\r\n      <DialogTrigger asChild>\r\n        <Button variant=\"secondary\">Sign in</Button>\r\n      </DialogTrigger>\r\n      <DialogContent>\r\n        <div className=\"flex flex-col items-center gap-2\">\r\n          <div\r\n            className=\"flex size-11 shrink-0 items-center justify-center rounded-full border\"\r\n            aria-hidden=\"true\"\r\n          >\r\n            <img src=\"/logo.webp\" alt=\"logo\" className=\"h-8 w-8 rounded-full\" />\r\n          </div>\r\n          <DialogHeader>\r\n            <DialogTitle className=\"sm:text-center\">Welcome back</DialogTitle>\r\n            <DialogDescription className=\"sm:text-center\">\r\n              Enter your credentials to login to your account.\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n        </div>\r\n\r\n        <form className=\"space-y-5\">\r\n          <div className=\"space-y-4\">\r\n            <div className=\"*:not-first:mt-2\">\r\n              <Label htmlFor={`${id}-email`}>Email</Label>\r\n              <Input\r\n                id={`${id}-email`}\r\n                placeholder=\"<EMAIL>\"\r\n                type=\"email\"\r\n                required\r\n              />\r\n            </div>\r\n            <div className=\"*:not-first:mt-2\">\r\n              <Label htmlFor={`${id}-password`}>Password</Label>\r\n              <Input\r\n                id={`${id}-password`}\r\n                placeholder=\"Enter your password\"\r\n                type=\"password\"\r\n                required\r\n              />\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-between gap-2\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <Checkbox id={`${id}-remember`} />\r\n              <Label\r\n                htmlFor={`${id}-remember`}\r\n                className=\"text-muted-foreground font-normal\"\r\n              >\r\n                Remember me\r\n              </Label>\r\n            </div>\r\n            <a className=\"text-sm underline hover:no-underline\" href=\"#\">\r\n              Forgot password?\r\n            </a>\r\n          </div>\r\n          <Button type=\"button\" className=\"w-full\">\r\n            Sign in\r\n          </Button>\r\n        </form>\r\n\r\n        <div className=\"before:bg-border after:bg-border flex items-center gap-3 before:h-px before:flex-1 after:h-px after:flex-1\">\r\n          <span className=\"text-muted-foreground text-xs\">Or</span>\r\n        </div>\r\n\r\n        <Button variant=\"outline\">Login with Google</Button>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n", "path": "/components/mvpblocks/basics/modals/signin-modal.tsx", "target": "/components/mvpblocks/signin-modal.tsx"}]}