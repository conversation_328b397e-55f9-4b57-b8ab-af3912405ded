{"name": "pulsating-loader", "type": "registry:block", "dependencies": ["framer-motion"], "registryDependencies": [], "files": [{"type": "registry:block", "content": "import { motion } from \"framer-motion\";\r\n\r\nexport default function PulsatingDots() {\r\n  return (\r\n    <div className=\"flex items-center justify-center\">\r\n      <div className=\"flex space-x-2\">\r\n        <motion.div\r\n          className=\"h-3 w-3 rounded-full bg-red-500\"\r\n          animate={{\r\n            scale: [1, 1.5, 1],\r\n            opacity: [0.5, 1, 0.5],\r\n          }}\r\n          transition={{\r\n            duration: 1,\r\n            ease: \"easeInOut\",\r\n            repeat: Infinity,\r\n          }}\r\n        />\r\n        <motion.div\r\n          className=\"h-3 w-3 rounded-full bg-red-500\"\r\n          animate={{\r\n            scale: [1, 1.5, 1],\r\n            opacity: [0.5, 1, 0.5],\r\n          }}\r\n          transition={{\r\n            duration: 1,\r\n            ease: \"easeInOut\",\r\n            repeat: Infinity,\r\n            delay: 0.3,\r\n          }}\r\n        />\r\n        <motion.div\r\n          className=\"h-3 w-3 rounded-full bg-red-500\"\r\n          animate={{\r\n            scale: [1, 1.5, 1],\r\n            opacity: [0.5, 1, 0.5],\r\n          }}\r\n          transition={{\r\n            duration: 1,\r\n            ease: \"easeInOut\",\r\n            repeat: Infinity,\r\n            delay: 0.6,\r\n          }}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/basics/loaders/pulsating-loader.tsx", "target": "/components/mvpblocks/pulsating-loader.tsx"}]}