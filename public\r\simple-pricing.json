{"name": "simple-pricing", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react", "@number-flow/react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/badge.json", "https://blocks.mvp-subha.me/r/button.json", "https://blocks.mvp-subha.me/r/card.json", "https://blocks.mvp-subha.me/r/tabs.json", "https://blocks.mvp-subha.me/r/utils.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport NumberF<PERSON> from \"@number-flow/react\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/components/ui/card\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { <PERSON><PERSON>les, ArrowRight, Check, Star, Zap, Shield } from \"lucide-react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\n\r\nconst plans = [\r\n  {\r\n    id: \"hobby\",\r\n    name: \"<PERSON>bby\",\r\n    icon: Star,\r\n    price: {\r\n      monthly: \"Free forever\",\r\n      yearly: \"Free forever\",\r\n    },\r\n    description:\r\n      \"The perfect starting place for your web app or personal project.\",\r\n    features: [\r\n      \"50 API calls / month\",\r\n      \"60 second checks\",\r\n      \"Single-user account\",\r\n      \"5 monitors\",\r\n      \"Basic email support\",\r\n    ],\r\n    cta: \"Get started for free\",\r\n  },\r\n  {\r\n    id: \"pro\",\r\n    name: \"<PERSON>\",\r\n    icon: Zap,\r\n    price: {\r\n      monthly: 90,\r\n      yearly: 75,\r\n    },\r\n    description: \"Everything you need to build and scale your business.\",\r\n    features: [\r\n      \"Unlimited API calls\",\r\n      \"30 second checks\",\r\n      \"Multi-user account\",\r\n      \"10 monitors\",\r\n      \"Priority email support\",\r\n    ],\r\n    cta: \"Subscribe to Pro\",\r\n    popular: true,\r\n  },\r\n  {\r\n    id: \"enterprise\",\r\n    name: \"Enterprise\",\r\n    icon: Shield,\r\n    price: {\r\n      monthly: \"Get in touch for pricing\",\r\n      yearly: \"Get in touch for pricing\",\r\n    },\r\n    description: \"Critical security, performance, observability and support.\",\r\n    features: [\r\n      \"You can DDOS our API.\",\r\n      \"Nano-second checks.\",\r\n      \"Invite your extended family.\",\r\n      \"Unlimited monitors.\",\r\n      \"We'll sit on your desk.\",\r\n    ],\r\n    cta: \"Contact us\",\r\n  },\r\n];\r\n\r\nexport default function SimplePricing() {\r\n  const [frequency, setFrequency] = useState<string>(\"monthly\");\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  if (!mounted) return null;\r\n\r\n  return (\r\n    <div className=\"not-prose w-full relative flex flex-col gap-16 px-4 py-24 text-center sm:px-8 overflow-hidden\">\r\n      <div className=\"absolute inset-0 -z-10 overflow-hidden\">\r\n        <div className=\"absolute -top-[10%] left-[50%] -translate-x-1/2 h-[40%] w-[60%] rounded-full bg-primary/10 blur-3xl\" />\r\n        <div className=\"absolute -bottom-[10%] -right-[10%] h-[40%] w-[40%] rounded-full bg-primary/5 blur-3xl\" />\r\n        <div className=\"absolute -bottom-[10%] -left-[10%] h-[40%] w-[40%] rounded-full bg-primary/5 blur-3xl\" />\r\n      </div>\r\n\r\n      <div className=\"flex flex-col items-center justify-center gap-8\">\r\n        <div className=\"flex flex-col items-center space-y-2\">\r\n          <Badge\r\n            variant=\"outline\"\r\n            className=\"mb-4 rounded-full px-4 py-1 text-sm font-medium border-primary/20 bg-primary/5\"\r\n          >\r\n            <Sparkles className=\"mr-1 h-3.5 w-3.5 text-primary animate-pulse\" />\r\n            Pricing Plans\r\n          </Badge>\r\n          <motion.h1\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5 }}\r\n            className=\"text-4xl sm:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-b from-foreground to-foreground/30\"\r\n          >\r\n            Pick the perfect plan for your needs\r\n          </motion.h1>\r\n          <motion.p\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5, delay: 0.1 }}\r\n            className=\"pt-2 text-lg text-muted-foreground max-w-md\"\r\n          >\r\n            Simple, transparent pricing that scales with your business. No hidden fees, no surprises.\r\n          </motion.p>\r\n        </div>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, scale: 0.95 }}\r\n          animate={{ opacity: 1, scale: 1 }}\r\n          transition={{ duration: 0.4, delay: 0.2 }}\r\n        >\r\n          <Tabs\r\n            defaultValue={frequency}\r\n            onValueChange={setFrequency}\r\n            className=\"inline-block bg-muted/30 p-1 rounded-full shadow-sm\"\r\n          >\r\n            <TabsList className=\"bg-transparent\">\r\n              <TabsTrigger\r\n                value=\"monthly\"\r\n                className=\"data-[state=active]:bg-background data-[state=active]:shadow-sm rounded-full transition-all duration-300\"\r\n              >\r\n                Monthly\r\n              </TabsTrigger>\r\n              <TabsTrigger\r\n                value=\"yearly\"\r\n                className=\"data-[state=active]:bg-background data-[state=active]:shadow-sm rounded-full transition-all duration-300\"\r\n              >\r\n                Yearly\r\n                <Badge variant=\"secondary\" className=\"ml-2 bg-primary/10 text-primary hover:bg-primary/15\">20% off</Badge>\r\n              </TabsTrigger>\r\n            </TabsList>\r\n          </Tabs>\r\n        </motion.div>\r\n\r\n        <div className=\"mt-8 grid w-full max-w-6xl grid-cols-1 gap-6 md:grid-cols-3\">\r\n          {plans.map((plan, index) => (\r\n            <motion.div\r\n              key={plan.id}\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.5, delay: 0.1 + index * 0.1 }}\r\n              whileHover={{ y: -5 }}\r\n              className=\"flex\"\r\n            >\r\n              <Card\r\n                className={cn(\r\n                  \"relative bg-secondary/20 w-full text-left h-full transition-all duration-300 hover:shadow-lg\",\r\n                  plan.popular\r\n                    ? \"ring-2 ring-primary/50 shadow-md dark:shadow-primary/10\"\r\n                    : \"hover:border-primary/30\",\r\n                  plan.popular && \"bg-gradient-to-b from-primary/[0.03] to-transparent\"\r\n                )}\r\n              >\r\n                {plan.popular && (\r\n                  <div className=\"absolute -top-3 left-0 right-0 mx-auto w-fit\">\r\n                    <Badge className=\"rounded-full px-4 py-1 bg-primary text-primary-foreground shadow-sm\">\r\n                      <Sparkles className=\"mr-1 h-3.5 w-3.5\" />\r\n                      Popular\r\n                    </Badge>\r\n                  </div>\r\n                )}\r\n                <CardHeader className={cn(\r\n                  \"pb-4\",\r\n                  plan.popular && \"pt-8\"\r\n                )}>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <div className={cn(\r\n                      \"flex h-8 w-8 items-center justify-center rounded-full\",\r\n                      plan.popular\r\n                        ? \"bg-primary/10 text-primary\"\r\n                        : \"bg-secondary text-foreground\"\r\n                    )}>\r\n                      <plan.icon className=\"h-4 w-4\" />\r\n                    </div>\r\n                    <CardTitle className={cn(\r\n                      \"text-xl font-bold\",\r\n                      plan.popular && \"text-primary\"\r\n                    )}>\r\n                      {plan.name}\r\n                    </CardTitle>\r\n                  </div>\r\n                  <CardDescription className=\"space-y-2 mt-3\">\r\n                    <p className=\"text-sm\">{plan.description}</p>\r\n                    <div className=\"pt-2\">\r\n                      {typeof plan.price[frequency as keyof typeof plan.price] === \"number\" ? (\r\n                        <div className=\"flex items-baseline\">\r\n                          <NumberFlow\r\n                            className={cn(\r\n                              \"text-3xl font-bold\",\r\n                              plan.popular ? \"text-primary\" : \"text-foreground\"\r\n                            )}\r\n                            format={{\r\n                              style: \"currency\",\r\n                              currency: \"USD\",\r\n                              maximumFractionDigits: 0,\r\n                            }}\r\n                            value={\r\n                              plan.price[\r\n                                frequency as keyof typeof plan.price\r\n                              ] as number\r\n                            }\r\n                          />\r\n                          <span className=\"ml-1 text-sm text-muted-foreground\">\r\n                            /month, billed {frequency}\r\n                          </span>\r\n                        </div>\r\n                      ) : (\r\n                        <span className={cn(\r\n                          \"text-2xl font-bold\",\r\n                          plan.popular ? \"text-primary\" : \"text-foreground\"\r\n                        )}>\r\n                          {plan.price[frequency as keyof typeof plan.price]}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </CardDescription>\r\n                </CardHeader>\r\n                <CardContent className=\"grid gap-3 pb-6\">\r\n                  {plan.features.map((feature, index) => (\r\n                    <motion.div\r\n                      key={index}\r\n                      initial={{ opacity: 0, x: -5 }}\r\n                      animate={{ opacity: 1, x: 0 }}\r\n                      transition={{ duration: 0.3, delay: 0.5 + (index * 0.05) }}\r\n                      className=\"flex items-center gap-2 text-sm\"\r\n                    >\r\n                      <div className={cn(\r\n                        \"flex h-5 w-5 items-center justify-center rounded-full\",\r\n                        plan.popular\r\n                          ? \"bg-primary/10 text-primary\"\r\n                          : \"bg-secondary text-secondary-foreground\"\r\n                      )}>\r\n                        <Check className=\"h-3.5 w-3.5\" />\r\n                      </div>\r\n                      <span className={plan.popular ? \"text-foreground\" : \"text-muted-foreground\"}>\r\n                        {feature}\r\n                      </span>\r\n                    </motion.div>\r\n                  ))}\r\n                </CardContent>\r\n                <CardFooter>\r\n                  <Button\r\n                    variant={plan.popular ? \"default\" : \"outline\"}\r\n                    className={cn(\r\n                      \"w-full font-medium transition-all duration-300\",\r\n                      plan.popular\r\n                        ? \"bg-primary hover:bg-primary/90 hover:shadow-md hover:shadow-primary/20\"\r\n                        : \"hover:bg-primary/5 hover:text-primary hover:border-primary/30\"\r\n                    )}\r\n                  >\r\n                    {plan.cta}\r\n                    <ArrowRight className=\"ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1\" />\r\n                  </Button>\r\n                </CardFooter>\r\n\r\n                {/* Subtle gradient effects */}\r\n                {plan.popular ? (\r\n                  <>\r\n                    <div className=\"absolute bottom-0 left-0 right-0 h-1/2 bg-gradient-to-t from-primary/[0.05] to-transparent rounded-b-lg pointer-events-none\" />\r\n                    <div className=\"absolute inset-0 rounded-lg border border-primary/20 pointer-events-none\" />\r\n                  </>\r\n                ) : (\r\n                  <div className=\"absolute inset-0 rounded-lg border border-transparent opacity-0 hover:opacity-100 hover:border-primary/10 pointer-events-none transition-opacity duration-300\" />\r\n                )}\r\n              </Card>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/pricing/simple-pricing.tsx", "target": "/components/mvpblocks/simple-pricing.tsx"}]}