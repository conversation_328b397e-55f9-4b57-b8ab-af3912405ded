{"name": "bento-grid-2", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/card.json"], "files": [{"type": "registry:block", "content": "\"use client\"\r\n\r\nimport type React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Home, MapPin, Compass, Building, Heart, HomeIcon, Camera } from \"lucide-react\"\r\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from \"@/components/ui/card\"\r\nimport { motion } from \"framer-motion\"\r\n\r\ninterface BentoItem {\r\n  title: string\r\n  description: string\r\n  icon: React.ReactNode\r\n  status?: string\r\n  tags?: string[]\r\n  meta?: string\r\n  cta?: string\r\n  colSpan?: number\r\n  hasPersistentHover?: boolean\r\n}\r\n\r\ninterface BentoGridProps {\r\n  items: BentoItem[]\r\n}\r\n\r\nconst itemsSample: BentoItem[] = [\r\n  {\r\n    title: \"Component Library\",\r\n    meta: \"100+ components\",\r\n    description:\r\n      \"Explore our extensive collection of ready-to-use UI components built with Next.js and Tailwind CSS. Perfect for quickly building beautiful, responsive websites.\",\r\n    icon: <Home className=\"w-4 h-4 text-primary\" />,\r\n    status: \"Popular\",\r\n    tags: [\"UI\", \"Components\", \"Tailwind\"],\r\n    colSpan: 2,\r\n    hasPersistentHover: true,\r\n  },\r\n  {\r\n    title: \"Responsive Design\",\r\n    meta: \"All devices\",\r\n    description: \"Every component is fully responsive and works beautifully on all screen sizes, from mobile to desktop.\",\r\n    icon: <Building className=\"w-4 h-4 text-primary\" />,\r\n    status: \"Essential\",\r\n    tags: [\"Mobile\", \"Desktop\"],\r\n  },\r\n  {\r\n    title: \"Theme Support\",\r\n    description: \"All components support both light and dark modes out of the box, with seamless transitions.\",\r\n    icon: <MapPin className=\"w-4 h-4 text-primary\" />,\r\n    status: \"New\",\r\n  },\r\n  {\r\n    title: \"Performance Optimized\",\r\n    description: \"Built with performance in mind, ensuring fast load times and smooth interactions.\",\r\n    icon: <HomeIcon className=\"w-4 h-4 text-primary\" />,\r\n    meta: \"Lighthouse 100\",\r\n    tags: [\"Speed\", \"Optimization\"],\r\n  },\r\n  {\r\n    title: \"Accessibility\",\r\n    description: \"All components follow WCAG guidelines and are fully accessible to all users.\",\r\n    icon: <Heart className=\"w-4 h-4 text-primary\" />,\r\n    meta: \"WCAG 2.1 AA\",\r\n    tags: [\"A11y\", \"Inclusive\"],\r\n  },\r\n  {\r\n    title: \"Developer Experience\",\r\n    meta: \"TypeScript\",\r\n    description: \"Clean, well-documented code with TypeScript support for a seamless development experience.\",\r\n    icon: <Compass className=\"w-4 h-4 text-primary\" />,\r\n    status: \"Featured\",\r\n    tags: [\"DX\", \"TypeScript\"],\r\n  },\r\n  {\r\n    title: \"Open Source\",\r\n    meta: \"MIT License\",\r\n    description:\r\n      \"MVPBlocks is completely free and open-source. Use it for personal and commercial projects without any restrictions or attribution requirements.\",\r\n    icon: <Camera className=\"w-4 h-4 text-primary\" />,\r\n    status: \"Free\",\r\n    tags: [\"Open Source\", \"MIT\"],\r\n    colSpan: 2,\r\n  },\r\n]\r\n\r\nexport default function BentoGrid({ items = itemsSample }: BentoGridProps) {\r\n  return (\r\n    <section className=\"py-12 relative overflow-hidden\">\r\n      {/* Decorative elements */}\r\n      <div className=\"absolute -left-20 top-20 h-64 w-64 rounded-full bg-primary/5 blur-3xl\" />\r\n      <div className=\"absolute -right-20 bottom-20 h-64 w-64 rounded-full bg-primary/5 blur-3xl\" />\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 p-4 max-w-6xl mx-auto relative\">\r\n        {items.map((item, index) => (\r\n          <motion.a\r\n            href=\"#\"\r\n            key={`${item.title}-${item.status || item.meta}`}\r\n            className={cn(item.colSpan || \"col-span-1\", item.colSpan === 2 ? \"md:col-span-2\" : \"\")}\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.4, delay: index * 0.1 }}\r\n          >\r\n            <Card\r\n              className={cn(\r\n                \"group relative bg-card/40 h-full transition-all duration-300 hover:shadow-md\",\r\n                \"hover:-translate-y-1 will-change-transform\",\r\n                \"overflow-hidden border-border/60\",\r\n                {\r\n                  \"shadow-md -translate-y-1\": item.hasPersistentHover,\r\n                },\r\n              )}\r\n            >\r\n              <div\r\n                className={cn(\r\n                  \"absolute inset-0\",\r\n                  item.hasPersistentHover ? \"opacity-100\" : \"opacity-0 group-hover:opacity-100\",\r\n                  \"transition-opacity duration-300\",\r\n                )}\r\n              >\r\n                <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(0,0,0,0.03)_1px,transparent_1px)] dark:bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.03)_1px,transparent_1px)] bg-[length:4px_4px]\" />\r\n              </div>\r\n\r\n              <CardHeader className=\"relative space-y-0 p-4\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"w-8 h-8 rounded-lg flex items-center justify-center bg-primary/10\">\r\n                    {item.icon}\r\n                  </div>\r\n                  <span className=\"text-xs font-medium px-2 py-1 rounded-md bg-secondary text-secondary-foreground\">\r\n                    {item.status || \"Active\"}\r\n                  </span>\r\n                </div>\r\n              </CardHeader>\r\n\r\n              <CardContent className=\"relative space-y-2 p-4 pt-0\">\r\n                <h3 className=\"font-medium text-foreground tracking-tight text-[15px]\">\r\n                  {item.title}\r\n                  {item.meta && (\r\n                    <span className=\"ml-2 text-xs text-muted-foreground font-normal\">{item.meta}</span>\r\n                  )}\r\n                </h3>\r\n                <p className=\"text-sm text-muted-foreground leading-relaxed\">{item.description}</p>\r\n              </CardContent>\r\n\r\n              <CardFooter className=\"relative p-4\">\r\n                <div className=\"flex items-center justify-between w-full\">\r\n                  <div className=\"flex flex-wrap gap-2 text-xs text-muted-foreground\">\r\n                    {item.tags?.map((tag) => (\r\n                      <span\r\n                        key={`${item.title}-${tag}`}\r\n                        className=\"px-2 py-1 rounded-md bg-secondary/50 backdrop-blur-xs transition-all duration-200\"\r\n                      >\r\n                        #{tag}\r\n                      </span>\r\n                    ))}\r\n                  </div>\r\n                  <span className=\"text-xs text-primary opacity-0 group-hover:opacity-100 transition-opacity font-medium\">\r\n                    {item.cta || \"Explore →\"}\r\n                  </span>\r\n                </div>\r\n              </CardFooter>\r\n\r\n              <div\r\n                className={cn(\r\n                  \"absolute inset-0 -z-10 rounded-xl p-px bg-gradient-to-br from-transparent via-primary/10 to-transparent\",\r\n                  item.hasPersistentHover ? \"opacity-100\" : \"opacity-0 group-hover:opacity-100\",\r\n                  \"transition-opacity duration-300\",\r\n                )}\r\n              />\r\n            </Card>\r\n          </motion.a>\r\n        ))}\r\n      </div>\r\n    </section>\r\n  )\r\n}", "path": "/components/mvpblocks/grids/bento-grid-2.tsx", "target": "/components/mvpblocks/bento-grid-2.tsx"}]}