{"name": "faq-2", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/badge.json"], "files": [{"type": "registry:block", "content": "\"use client\"\r\n\r\nimport { useState } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { MinusIcon, PlusIcon } from \"lucide-react\";\r\n\r\ninterface FaqItem {\r\n  id: string;\r\n  question: string;\r\n  answer: string;\r\n  category: \"general\" | \"pricing\" | \"technical\" | \"support\";\r\n}\r\n\r\nconst faqItems: FaqItem[] = [\r\n  {\r\n    id: \"1\",\r\n    question: \"What is MVPBlocks?\",\r\n    answer: \"MVPBlocks is a collection of ready-to-use UI components built with Next.js and Tailwind CSS. It helps developers quickly build beautiful, responsive websites without starting from scratch.\",\r\n    category: \"general\",\r\n  },\r\n  {\r\n    id: \"2\",\r\n    question: \"Is MVPBlocks free to use?\",\r\n    answer: \"Yes, MVPBlocks is completely free and open-source. You can use it for personal and commercial projects without any restrictions or attribution requirements.\",\r\n    category: \"general\",\r\n  },\r\n  {\r\n    id: \"3\",\r\n    question: \"Do I need to know Tailwind CSS to use MVPBlocks?\",\r\n    answer: \"While having Tailwind CSS knowledge is helpful, it's not required. You can simply copy and paste our components into your project and make basic modifications without deep Tailwind expertise.\",\r\n    category: \"technical\",\r\n  },\r\n  {\r\n    id: \"4\",\r\n    question: \"How do I install MVPBlocks?\",\r\n    answer: \"You don't need to install MVPBlocks as a package. Simply browse our component library, find the components you need, and copy the code into your project. Make sure you have the required dependencies installed.\",\r\n    category: \"technical\",\r\n  },\r\n  {\r\n    id: \"5\",\r\n    question: \"Can I customize the components?\",\r\n    answer: \"Absolutely! All components are built with customization in mind. You can modify colors, spacing, typography, and more using Tailwind classes or by editing the component code directly.\",\r\n    category: \"technical\",\r\n  },\r\n  {\r\n    id: \"6\",\r\n    question: \"Do MVPBlocks components work with dark mode?\",\r\n    answer: \"Yes, all MVPBlocks components are designed to work seamlessly with both light and dark modes. They automatically adapt to your site's theme settings.\",\r\n    category: \"technical\",\r\n  },\r\n  {\r\n    id: \"7\",\r\n    question: \"How often are new components added?\",\r\n    answer: \"We regularly add new components to the library. Our goal is to provide a comprehensive set of components for all common UI patterns and website sections.\",\r\n    category: \"general\",\r\n  },\r\n  {\r\n    id: \"8\",\r\n    question: \"How can I contribute to MVPBlocks?\",\r\n    answer: \"We welcome contributions! You can contribute by creating new components, improving existing ones, fixing bugs, or enhancing documentation. Check our GitHub repository for contribution guidelines.\",\r\n    category: \"support\",\r\n  },\r\n];\r\n\r\nconst categories = [\r\n  { id: \"all\", label: \"All\" },\r\n  { id: \"general\", label: \"General\" },\r\n  { id: \"technical\", label: \"Technical\" },\r\n  { id: \"pricing\", label: \"Pricing\" },\r\n  { id: \"support\", label: \"Support\" },\r\n];\r\n\r\nexport default function Faq2() {\r\n  const [activeCategory, setActiveCategory] = useState<string>(\"all\");\r\n  const [expandedId, setExpandedId] = useState<string | null>(null);\r\n\r\n  const filteredFaqs = activeCategory === \"all\"\r\n    ? faqItems\r\n    : faqItems.filter(item => item.category === activeCategory);\r\n\r\n  const toggleExpand = (id: string) => {\r\n    setExpandedId(expandedId === id ? null : id);\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-16 bg-background\">\r\n      <div className=\"container mx-auto max-w-6xl px-4 md:px-6\">\r\n        <div className=\"flex flex-col items-center mb-12\">\r\n          <Badge\r\n            variant=\"outline\"\r\n            className=\"mb-4 px-3 py-1 text-xs font-medium uppercase tracking-wider border-primary\"\r\n          >\r\n            FAQs\r\n          </Badge>\r\n\r\n          <h2 className=\"text-4xl md:text-5xl font-bold text-center mb-6 tracking-tight text-foreground\">\r\n            Frequently Asked Questions\r\n          </h2>\r\n\r\n          <p className=\"text-muted-foreground text-center max-w-2xl\">\r\n            Find answers to common questions about MVPBlocks and how to use our components to build your next project.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Category Tabs */}\r\n        <div className=\"flex flex-wrap justify-center gap-2 mb-10\">\r\n          {categories.map((category) => (\r\n            <button\r\n              key={category.id}\r\n              onClick={() => setActiveCategory(category.id)}\r\n              className={cn(\r\n                \"px-4 py-2 rounded-full text-sm font-medium transition-all\",\r\n                activeCategory === category.id\r\n                  ? \"bg-primary text-primary-foreground\"\r\n                  : \"bg-secondary text-secondary-foreground hover:bg-secondary/80\"\r\n              )}\r\n            >\r\n              {category.label}\r\n            </button>\r\n          ))}\r\n        </div>\r\n\r\n        {/* FAQ Grid */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <AnimatePresence>\r\n            {filteredFaqs.map((faq, index) => (\r\n              <motion.div\r\n                key={faq.id}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                exit={{ opacity: 0, y: -20 }}\r\n                transition={{ duration: 0.3, delay: index * 0.05 }}\r\n                className={cn(\r\n                  \"border border-border rounded-xl overflow-hidden h-fit\",\r\n                  expandedId === faq.id ? \"bg-card/50 shadow-3xl\" : \"bg-card/50\"\r\n                )}\r\n                style={{ minHeight: '88px' }}\r\n              >\r\n                <button\r\n                  onClick={() => toggleExpand(faq.id)}\r\n                  className=\"w-full flex items-center justify-between p-6 text-left\"\r\n                >\r\n                  <h3 className=\"text-lg font-medium text-foreground\">{faq.question}</h3>\r\n                  <div className=\"ml-4 flex-shrink-0\">\r\n                    {expandedId === faq.id ? (\r\n                      <MinusIcon className=\"h-5 w-5 text-primary\" />\r\n                    ) : (\r\n                      <PlusIcon className=\"h-5 w-5 text-primary\" />\r\n                    )}\r\n                  </div>\r\n                </button>\r\n\r\n                <AnimatePresence>\r\n                  {expandedId === faq.id && (\r\n                    <motion.div\r\n                      initial={{ height: 0, opacity: 0 }}\r\n                      animate={{ height: \"auto\", opacity: 1 }}\r\n                      exit={{ height: 0, opacity: 0 }}\r\n                      transition={{ duration: 0.3 }}\r\n                      className=\"overflow-hidden\"\r\n                    >\r\n                      <div className=\"px-6 pb-6 pt-2 border-t border-border\">\r\n                        <p className=\"text-muted-foreground\">{faq.answer}</p>\r\n                      </div>\r\n                    </motion.div>\r\n                  )}\r\n                </AnimatePresence>\r\n              </motion.div>\r\n            ))}\r\n          </AnimatePresence>\r\n        </div>\r\n\r\n        {/* Contact CTA */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.5, duration: 0.5 }}\r\n          className=\"mt-16 text-center\"\r\n        >\r\n          <p className=\"text-muted-foreground mb-4\">\r\n            Can&apos;t find what you&apos;re looking for?\r\n          </p>\r\n          <a\r\n            href=\"#\"\r\n            className=\"inline-flex items-center justify-center px-6 py-3 border-2 border-primary rounded-lg font-medium text-foreground hover:bg-primary hover:text-primary-foreground transition-colors\"\r\n          >\r\n            Contact Support\r\n          </a>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n}", "path": "/components/mvpblocks/mainsections/faqs/faq-2.tsx", "target": "/components/mvpblocks/faq-2.tsx"}]}