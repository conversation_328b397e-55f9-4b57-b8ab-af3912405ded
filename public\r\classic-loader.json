{"name": "classic-loader", "type": "registry:block", "dependencies": [], "registryDependencies": [], "files": [{"type": "registry:block", "content": "export default function ClassicLoader() {\r\n  return (\r\n    <div className=\"flex h-10 w-10 animate-spin items-center justify-center rounded-full border-4 border-primary border-t-transparent\"></div>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/basics/loaders/classic-loader.tsx", "target": "/components/mvpblocks/classic-loader.tsx"}]}