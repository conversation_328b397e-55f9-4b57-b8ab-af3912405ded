{"name": "skeleton", "type": "registry:ui", "files": [{"type": "registry:ui", "content": "import { cn } from \"@/lib/utils\";\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n", "path": "/components/ui/skeleton.tsx", "target": "/components/ui/skeleton.tsx"}]}