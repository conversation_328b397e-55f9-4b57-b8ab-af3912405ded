{"name": "menubar", "type": "registry:ui", "dependencies": ["@radix-ui/react-menubar"], "files": [{"type": "registry:ui", "content": "\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as MenubarPrimitive from \"@radix-ui/react-menubar\";\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nfunction MenubarMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof MenubarPrimitive.Menu>) {\r\n  return <MenubarPrimitive.Menu {...props} />;\r\n}\r\n\r\nfunction MenubarGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof MenubarPrimitive.Group>) {\r\n  return <MenubarPrimitive.Group {...props} />;\r\n}\r\n\r\nfunction MenubarPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof MenubarPrimitive.Portal>) {\r\n  return <MenubarPrimitive.Portal {...props} />;\r\n}\r\n\r\nfunction MenubarRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof MenubarPrimitive.RadioGroup>) {\r\n  return <MenubarPrimitive.RadioGroup {...props} />;\r\n}\r\n\r\nfunction MenubarSub({\r\n  ...props\r\n}: React.ComponentProps<typeof MenubarPrimitive.Sub>) {\r\n  return <MenubarPrimitive.Sub data-slot=\"menubar-sub\" {...props} />;\r\n}\r\n\r\nconst Menubar = React.forwardRef<\r\n  React.ElementRef<typeof MenubarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <MenubarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-10 items-center space-x-1 rounded-md border bg-background p-1\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nMenubar.displayName = MenubarPrimitive.Root.displayName;\r\n\r\nconst MenubarTrigger = React.forwardRef<\r\n  React.ElementRef<typeof MenubarPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <MenubarPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default select-none items-center rounded-sm px-3 py-1.5 text-sm font-medium outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nMenubarTrigger.displayName = MenubarPrimitive.Trigger.displayName;\r\n\r\nconst MenubarSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof MenubarPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.SubTrigger> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <MenubarPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\r\n  </MenubarPrimitive.SubTrigger>\r\n));\r\nMenubarSubTrigger.displayName = MenubarPrimitive.SubTrigger.displayName;\r\n\r\nconst MenubarSubContent = React.forwardRef<\r\n  React.ElementRef<typeof MenubarPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <MenubarPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] origin-[--radix-menubar-content-transform-origin] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nMenubarSubContent.displayName = MenubarPrimitive.SubContent.displayName;\r\n\r\nconst MenubarContent = React.forwardRef<\r\n  React.ElementRef<typeof MenubarPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Content>\r\n>(\r\n  (\r\n    { className, align = \"start\", alignOffset = -4, sideOffset = 8, ...props },\r\n    ref,\r\n  ) => (\r\n    <MenubarPrimitive.Portal>\r\n      <MenubarPrimitive.Content\r\n        ref={ref}\r\n        align={align}\r\n        alignOffset={alignOffset}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"z-50 min-w-[12rem] origin-[--radix-menubar-content-transform-origin] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n          className,\r\n        )}\r\n        {...props}\r\n      />\r\n    </MenubarPrimitive.Portal>\r\n  ),\r\n);\r\nMenubarContent.displayName = MenubarPrimitive.Content.displayName;\r\n\r\nconst MenubarItem = React.forwardRef<\r\n  React.ElementRef<typeof MenubarPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Item> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <MenubarPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nMenubarItem.displayName = MenubarPrimitive.Item.displayName;\r\n\r\nconst MenubarCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof MenubarPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <MenubarPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <MenubarPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </MenubarPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </MenubarPrimitive.CheckboxItem>\r\n));\r\nMenubarCheckboxItem.displayName = MenubarPrimitive.CheckboxItem.displayName;\r\n\r\nconst MenubarRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof MenubarPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <MenubarPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <MenubarPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </MenubarPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </MenubarPrimitive.RadioItem>\r\n));\r\nMenubarRadioItem.displayName = MenubarPrimitive.RadioItem.displayName;\r\n\r\nconst MenubarLabel = React.forwardRef<\r\n  React.ElementRef<typeof MenubarPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Label> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <MenubarPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nMenubarLabel.displayName = MenubarPrimitive.Label.displayName;\r\n\r\nconst MenubarSeparator = React.forwardRef<\r\n  React.ElementRef<typeof MenubarPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <MenubarPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nMenubarSeparator.displayName = MenubarPrimitive.Separator.displayName;\r\n\r\nconst MenubarShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\r\n        \"ml-auto text-xs tracking-widest text-muted-foreground\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n};\r\nMenubarShortcut.displayname = \"MenubarShortcut\";\r\n\r\nexport {\r\n  Menubar,\r\n  MenubarMenu,\r\n  MenubarTrigger,\r\n  MenubarContent,\r\n  MenubarItem,\r\n  MenubarSeparator,\r\n  MenubarLabel,\r\n  MenubarCheckboxItem,\r\n  MenubarRadioGroup,\r\n  MenubarRadioItem,\r\n  MenubarPortal,\r\n  MenubarSubContent,\r\n  MenubarSubTrigger,\r\n  MenubarGroup,\r\n  MenubarSub,\r\n  MenubarShortcut,\r\n};\r\n", "path": "/components/ui/menubar.tsx", "target": "/components/ui/menubar.tsx"}]}