---
title: Chatbot UI
description: A great chatbot UI design can make a huge difference in user engagement and satisfaction. Here are some of the best chatbot UI designs to inspire you.
---

import {ComponentPreview} from "@/components/preview/component-preview";
import {extractSourceCode} from "@/lib/code";

## v0 Chat

<ComponentPreview
  name="v0-chat"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('v0-chat')).code}
  lang="tsx"
/>

## Bolt.New

<ComponentPreview
  name="bolt"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('bolt')).code}
  lang="tsx"
/>

## Animated AI Chat

<ComponentPreview
  name="animated-ai-chat"
  classNameComponentContainer="h-[600px]"
  code={(await extractSourceCode('animated-ai-chat')).code}
  lang="tsx"
  fromDocs={true}
/>
