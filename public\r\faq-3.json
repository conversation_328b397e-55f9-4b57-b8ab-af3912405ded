{"name": "faq-3", "type": "registry:block", "dependencies": ["framer-motion", "lucide-react", "react"], "registryDependencies": ["https://blocks.mvp-subha.me/r/utils.json", "https://blocks.mvp-subha.me/r/badge.json"], "files": [{"type": "registry:block", "content": "\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { ChevronDown, Mail } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\n\r\ninterface FAQItemProps {\r\n  question: string;\r\n  answer: string;\r\n  index: number;\r\n}\r\n\r\nfunction FAQItem({ question, answer, index }: FAQItemProps) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 10 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{\r\n        duration: 0.3,\r\n        delay: index * 0.15,\r\n        ease: \"easeOut\",\r\n      }}\r\n      className={cn(\r\n        \"group rounded-lg border border-border/60\",\r\n        \"transition-all duration-200 ease-in-out\",\r\n        isOpen ? \"bg-card/30 shadow-sm\" : \"hover:bg-card/50\",\r\n      )}\r\n    >\r\n      <button\r\n        type=\"button\"\r\n        onClick={() => setIsOpen(!isOpen)}\r\n        className=\"flex w-full items-center justify-between gap-4 px-6 py-4\"\r\n      >\r\n        <h3\r\n          className={cn(\r\n            \"text-left text-base font-medium transition-colors duration-200\",\r\n            \"text-foreground/80\",\r\n            isOpen && \"text-foreground\",\r\n          )}\r\n        >\r\n          {question}\r\n        </h3>\r\n        <motion.div\r\n          animate={{\r\n            rotate: isOpen ? 180 : 0,\r\n            scale: isOpen ? 1.1 : 1,\r\n          }}\r\n          transition={{\r\n            duration: 0.3,\r\n            ease: \"easeInOut\",\r\n          }}\r\n          className={cn(\r\n            \"shrink-0 rounded-full p-0.5\",\r\n            \"transition-colors duration-200\",\r\n            isOpen ? \"text-primary\" : \"text-muted-foreground\",\r\n          )}\r\n        >\r\n          <ChevronDown className=\"h-4 w-4\" />\r\n        </motion.div>\r\n      </button>\r\n      <AnimatePresence initial={false}>\r\n        {isOpen && (\r\n          <motion.div\r\n            initial={{ height: 0, opacity: 0 }}\r\n            animate={{\r\n              height: \"auto\",\r\n              opacity: 1,\r\n              transition: {\r\n                height: {\r\n                  duration: 0.4,\r\n                  ease: [0.04, 0.62, 0.23, 0.98],\r\n                },\r\n                opacity: {\r\n                  duration: 0.25,\r\n                  delay: 0.1,\r\n                },\r\n              },\r\n            }}\r\n            exit={{\r\n              height: 0,\r\n              opacity: 0,\r\n              transition: {\r\n                height: {\r\n                  duration: 0.3,\r\n                  ease: \"easeInOut\",\r\n                },\r\n                opacity: {\r\n                  duration: 0.25,\r\n                },\r\n              },\r\n            }}\r\n          >\r\n            <div className=\"border-t border-border/40 px-6 pb-4 pt-2\">\r\n              <motion.p\r\n                initial={{ y: -8, opacity: 0 }}\r\n                animate={{ y: 0, opacity: 1 }}\r\n                exit={{ y: -8, opacity: 0 }}\r\n                transition={{\r\n                  duration: 0.3,\r\n                  ease: \"easeOut\",\r\n                }}\r\n                className=\"text-sm leading-relaxed text-muted-foreground\"\r\n              >\r\n                {answer}\r\n              </motion.p>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n    </motion.div>\r\n  );\r\n}\r\n\r\nexport default function Faq3() {\r\n  const faqs: Omit<FAQItemProps, \"index\">[] = [\r\n    {\r\n      question: \"What makes MVPBlocks unique?\",\r\n      answer:\r\n        \"MVPBlocks stands out through its intuitive design, powerful component library, and seamless integration options. We've focused on creating a user experience that combines simplicity with advanced features, all while maintaining excellent performance and accessibility.\",\r\n    },\r\n    {\r\n      question: \"How can I customize the components?\",\r\n      answer:\r\n        \"All components are built with Tailwind CSS, making them highly customizable. You can modify colors, spacing, typography, and more by simply adjusting the class names or using our theme variables to match your brand identity.\",\r\n    },\r\n    {\r\n      question: \"Do the components work with dark mode?\",\r\n      answer:\r\n        \"Yes, all MVPBlocks components are designed to work seamlessly with both light and dark modes. They automatically adapt to your site's theme settings, providing a consistent user experience regardless of the user's preference.\",\r\n    },\r\n    {\r\n      question: \"How can I get started with MVPBlocks?\",\r\n      answer:\r\n        \"You can get started by browsing our component library and copying the code for the components you need. Our documentation provides clear instructions for installation and usage, and you can always reach out to our support team if you need assistance.\",\r\n    },\r\n    {\r\n      question: \"Can I use MVPBlocks for commercial projects?\",\r\n      answer:\r\n        \"Absolutely! MVPBlocks is free to use for both personal and commercial projects. There are no licensing fees or attribution requirements—just build and launch your MVP faster than ever before.\",\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <section className=\"relative w-full overflow-hidden bg-background py-16\">\r\n      {/* Decorative elements */}\r\n      <div className=\"absolute -left-20 top-20 h-64 w-64 rounded-full bg-primary/5 blur-3xl\" />\r\n      <div className=\"absolute -right-20 bottom-20 h-64 w-64 rounded-full bg-primary/5 blur-3xl\" />\r\n\r\n      <div className=\"container relative mx-auto max-w-6xl px-4\">\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.5 }}\r\n          className=\"mx-auto mb-12 max-w-2xl text-center\"\r\n        >\r\n          <Badge\r\n            variant=\"outline\"\r\n            className=\"mb-4 border-primary px-3 py-1 text-xs font-medium uppercase tracking-wider\"\r\n          >\r\n            FAQs\r\n          </Badge>\r\n\r\n          <h2 className=\"mb-3 bg-gradient-to-r from-primary to-rose-400 bg-clip-text text-3xl font-bold text-transparent\">\r\n            Frequently Asked Questions\r\n          </h2>\r\n          <p className=\"text-sm text-muted-foreground\">\r\n            Everything you need to know about MVPBlocks\r\n          </p>\r\n        </motion.div>\r\n\r\n        <div className=\"mx-auto max-w-2xl space-y-2\">\r\n          {faqs.map((faq, index) => (\r\n            <FAQItem key={index} {...faq} index={index} />\r\n          ))}\r\n        </div>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.5, delay: 0.3 }}\r\n          className={cn(\"mx-auto mt-12 max-w-md rounded-lg p-6 text-center\")}\r\n        >\r\n          <div className=\"mb-4 inline-flex items-center justify-center rounded-full bg-primary/10 p-2 text-primary\">\r\n            <Mail className=\"h-4 w-4\" />\r\n          </div>\r\n          <p className=\"mb-1 text-sm font-medium text-foreground\">\r\n            Still have questions?\r\n          </p>\r\n          <p className=\"mb-4 text-xs text-muted-foreground\">\r\n            We&apos;re here to help you\r\n          </p>\r\n          <button\r\n            type=\"button\"\r\n            className={cn(\r\n              \"rounded-md px-4 py-2 text-sm\",\r\n              \"bg-primary text-primary-foreground\",\r\n              \"hover:bg-primary/90\",\r\n              \"transition-colors duration-200\",\r\n              \"font-medium\",\r\n            )}\r\n          >\r\n            Contact Support\r\n          </button>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/mainsections/faqs/faq-3.tsx", "target": "/components/mvpblocks/faq-3.tsx"}]}