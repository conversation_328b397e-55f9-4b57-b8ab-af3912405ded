{"name": "context-menu", "type": "registry:ui", "dependencies": ["@radix-ui/react-context-menu"], "files": [{"type": "registry:ui", "content": "\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as ContextMenuPrimitive from \"@radix-ui/react-context-menu\";\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst ContextMenu = ContextMenuPrimitive.Root;\r\n\r\nconst ContextMenuTrigger = ContextMenuPrimitive.Trigger;\r\n\r\nconst ContextMenuGroup = ContextMenuPrimitive.Group;\r\n\r\nconst ContextMenuPortal = ContextMenuPrimitive.Portal;\r\n\r\nconst ContextMenuSub = ContextMenuPrimitive.Sub;\r\n\r\nconst ContextMenuRadioGroup = ContextMenuPrimitive.RadioGroup;\r\n\r\nconst ContextMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof ContextMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <ContextMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\r\n  </ContextMenuPrimitive.SubTrigger>\r\n));\r\nContextMenuSubTrigger.displayName = ContextMenuPrimitive.SubTrigger.displayName;\r\n\r\nconst ContextMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof ContextMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <ContextMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] origin-[--radix-context-menu-content-transform-origin] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nContextMenuSubContent.displayName = ContextMenuPrimitive.SubContent.displayName;\r\n\r\nconst ContextMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof ContextMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <ContextMenuPrimitive.Portal>\r\n    <ContextMenuPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"z-50 max-h-[--radix-context-menu-content-available-height] min-w-[8rem] origin-[--radix-context-menu-content-transform-origin] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md animate-in fade-in-80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  </ContextMenuPrimitive.Portal>\r\n));\r\nContextMenuContent.displayName = ContextMenuPrimitive.Content.displayName;\r\n\r\nconst ContextMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof ContextMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Item> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <ContextMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nContextMenuItem.displayName = ContextMenuPrimitive.Item.displayName;\r\n\r\nconst ContextMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof ContextMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <ContextMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <ContextMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </ContextMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </ContextMenuPrimitive.CheckboxItem>\r\n));\r\nContextMenuCheckboxItem.displayName =\r\n  ContextMenuPrimitive.CheckboxItem.displayName;\r\n\r\nconst ContextMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof ContextMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <ContextMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <ContextMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </ContextMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </ContextMenuPrimitive.RadioItem>\r\n));\r\nContextMenuRadioItem.displayName = ContextMenuPrimitive.RadioItem.displayName;\r\n\r\nconst ContextMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof ContextMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Label> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <ContextMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold text-foreground\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nContextMenuLabel.displayName = ContextMenuPrimitive.Label.displayName;\r\n\r\nconst ContextMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof ContextMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <ContextMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-border\", className)}\r\n    {...props}\r\n  />\r\n));\r\nContextMenuSeparator.displayName = ContextMenuPrimitive.Separator.displayName;\r\n\r\nconst ContextMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\r\n        \"ml-auto text-xs tracking-widest text-muted-foreground\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n};\r\nContextMenuShortcut.displayName = \"ContextMenuShortcut\";\r\n\r\nexport {\r\n  ContextMenu,\r\n  ContextMenuTrigger,\r\n  ContextMenuContent,\r\n  ContextMenuItem,\r\n  ContextMenuCheckboxItem,\r\n  ContextMenuRadioItem,\r\n  ContextMenuLabel,\r\n  ContextMenuSeparator,\r\n  ContextMenuShortcut,\r\n  ContextMenuGroup,\r\n  ContextMenuPortal,\r\n  ContextMenuSub,\r\n  ContextMenuSubContent,\r\n  ContextMenuSubTrigger,\r\n  ContextMenuRadioGroup,\r\n};\r\n", "path": "/components/ui/context-menu.tsx", "target": "/components/ui/context-menu.tsx"}]}