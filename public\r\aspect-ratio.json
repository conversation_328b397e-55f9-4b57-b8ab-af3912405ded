{"name": "aspect-ratio", "type": "registry:ui", "dependencies": ["@radix-ui/react-aspect-ratio"], "files": [{"type": "registry:ui", "content": "\"use client\";\r\n\r\nimport * as AspectRatioPrimitive from \"@radix-ui/react-aspect-ratio\";\r\n\r\nconst AspectRatio = AspectRatioPrimitive.Root;\r\n\r\nexport { AspectRatio };\r\n", "path": "/components/ui/aspect-ratio.tsx", "target": "/components/ui/aspect-ratio.tsx"}]}