{"name": "btn-gradient1", "type": "registry:block", "dependencies": [], "registryDependencies": ["https://blocks.mvp-subha.me/r/button.json"], "files": [{"type": "registry:block", "content": "import { Button } from \"@/components/ui/button\";\r\n\r\nexport default function Btn09() {\r\n  return (\r\n    <Button className=\"group relative inline-flex items-center justify-center overflow-hidden rounded-md bg-gray-800 px-8 py-2.5 tracking-tighter text-white\">\r\n      <span className=\"absolute h-0 w-0 rounded-full bg-orange-600 transition-all duration-500 ease-out group-hover:h-56 group-hover:w-56\"></span>\r\n      <span className=\"absolute bottom-0 left-0 -ml-2 h-full\">\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          className=\"object-stretch h-full w-auto opacity-100\"\r\n          viewBox=\"0 0 487 487\"\r\n        >\r\n          <path\r\n            fillOpacity=\".1\"\r\n            fillRule=\"nonzero\"\r\n            fill=\"#FFF\"\r\n            d=\"M0 .3c67 2.1 134.1 4.3 186.3 37 52.2 32.7 89.6 95.8 112.8 150.6 23.2 54.8 32.3 101.4 61.2 149.9 28.9 48.4 77.7 98.8 126.4 149.2H0V.3z\"\r\n          ></path>\r\n        </svg>\r\n      </span>\r\n      <span className=\"absolute right-0 top-0 -mr-3 h-full w-12\">\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          className=\"h-full w-full object-cover\"\r\n          viewBox=\"0 0 487 487\"\r\n        >\r\n          <path\r\n            fillOpacity=\".1\"\r\n            fillRule=\"nonzero\"\r\n            fill=\"#FFF\"\r\n            d=\"M487 486.7c-66.1-3.6-132.3-7.3-186.3-37s-95.9-85.3-126.2-137.2c-30.4-51.8-49.3-99.9-76.5-151.4C70.9 109.6 35.6 54.8.3 0H487v486.7z\"\r\n          ></path>\r\n        </svg>\r\n      </span>\r\n      <span className=\"absolute inset-0 -mt-1 h-full w-full rounded-lg bg-gradient-to-b from-transparent via-transparent to-gray-200 opacity-30\"></span>\r\n      <span className=\"relative text-base font-semibold\">Hover Me !</span>\r\n    </Button>\r\n  );\r\n}\r\n", "path": "/components/mvpblocks/basics/buttons/btn-gradient1.tsx", "target": "/components/mvpblocks/btn-gradient1.tsx"}]}